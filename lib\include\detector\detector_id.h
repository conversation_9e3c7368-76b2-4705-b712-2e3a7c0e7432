// Copyright (C) 2020 CZUR Limited. All rights reserved.
// Only CZUR inner use.

#ifndef CZCV_CAMERA_DETECTOR_ID_H
#define CZCV_CAMERA_DETECTOR_ID_H

namespace czcv_camera
{
    enum class DetectorID
    {
        MTCNN_FACE        = 0,
        MobileV3_HEAD     = 1,
        YoloFastest_HEAD  = 2,
        NanoDet_ShuffleV2 = 3,
        Yolox_PERSON      = 4,
        CenterCrop_Demo_Det = 5, // actually no any detection, only for test
    };
}//namespace czcv_camera

#endif //CZCV_CAMERA_DETECTOR_ID_H

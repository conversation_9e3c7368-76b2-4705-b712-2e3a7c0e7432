// Copyright (C) 2020 CZUR Limited. All rights reserved.
// Only CZUR inner use.

#include <opencv2/opencv.hpp>

#ifndef CZCV_MOBILE_INTELLIGENCE_NMS_H
#define CZCV_MOBILE_INTELLIGENCE_NMS_H

namespace  czcv
{

    void NMSBoxes(const std::vector<cv::Rect>& bboxes, const std::vector<float>& scores,
                  const float score_threshold, const float nms_threshold,
                  std::vector<int>& indices, const float eta = 1.f, const int top_k = 0);

    void NMSBoxes(const std::vector<cv::Rect2d>& bboxes, const std::vector<float>& scores,
                  const float score_threshold, const float nms_threshold,
                  std::vector<int>& indices, const float eta = 1.f, const int top_k = 0);
}
#endif //CZCV_MOBILE_INTELLIGENCE_NMS_H

// Copyright (C) 2020 CZUR Limited. All rights reserved.
// Only CZUR inner use.

#ifndef CZCV_CAMERA_RECOGNIZER_ID_H
#define CZCV_CAMERA_RECOGNIZER_ID_H

namespace czcv_camera
{
    enum class TrackerID {
        DefaultTracker = 0,///
        KCF = 1, ///<
        fDSST = 2, ///<
        MOSSE = 3,  ///<
        IOU =4,
	    BYTE = 5
    };
}//namespace czcv_camera

#endif //CZCV_CAMERA_RECOGNIZER_ID_H

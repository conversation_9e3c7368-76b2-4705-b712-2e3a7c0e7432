#include "center_stage/czcv_center_stage.h"
#include "center_stage/center_stage_api.h"
#include "center_stage/person_viewer.h"
using namespace czcv_camera;

 void bgr2nv12(const cv::Mat & bgr, cv::Mat& nv12)
{
    cv::Mat yuv;
    cv::cvtColor(bgr, yuv, cv::COLOR_BGR2YUV_YV12);

    nv12 = cv::Mat::zeros(yuv.rows, yuv.cols, CV_8UC1);
    memcpy(nv12.data, yuv.data, bgr.rows * bgr.cols);
    unsigned char* pnv21v = (unsigned char*)nv12.data + bgr.rows * bgr.cols + 1;
    unsigned char* pnv21u = (unsigned char*)nv12.data + bgr.rows * bgr.cols;
    unsigned char* pyuv_v = (unsigned char*)yuv.data + bgr.rows * bgr.cols;
    unsigned char* pyuv_u = (unsigned char*)yuv.data + bgr.rows * bgr.cols + bgr.rows * bgr.cols / 4;
    for (size_t i = 0; i < bgr.rows * bgr.cols / 4; i++)
    {
        *pnv21u = *pyuv_v;
        *pnv21v = *pyuv_u;

        pyuv_v++;
        pyuv_u++;
        pnv21v += 2;
        pnv21u += 2;
    }
}

int main()
{
    czcv_camera::Android_API* cameraAlg = new czcv_camera::Android_API();
    std::string modelpath = "";
    int src_width = 3840;
    int src_height = 2160;
    int dst_width = 3840;    
    int dst_height = 2160;
    bool only_cpu = false;
    bool external_alg = false;
    int gesture_mode = 1;
    czcv_doa_callback pfun_doa_callback = nullptr;
    czcv_gesture_event_callback pfun_gesture_event_callback = nullptr;
    czcv_camera_led_callback pfun_camera_led_callback = nullptr;
    int ret = cameraAlg->init_api(modelpath, src_width, src_height, dst_width, dst_height, true, only_cpu,external_alg, gesture_mode, pfun_doa_callback, pfun_gesture_event_callback, pfun_camera_led_callback);
    if (ret != 0)
    {
        LOGE("init_api failed\n");
        return -1;
    }

    cv::Mat bgr = cv::Mat::zeros(src_height, src_width, CV_8UC3);
    cv::Mat frame;
    bgr2nv12(bgr, frame);

    int sub_phy_addr = 1;
    ret = cameraAlg->run_api_sub(frame, sub_phy_addr);

    int phy_addr = 0;
    void* dst_vir_addr = nullptr;
    int dst_phy_addr = -1;
    int Cmode = 1;
    int low_consumption = 0;
    ret = cameraAlg->run_api(frame, phy_addr, dst_vir_addr, dst_phy_addr,Cmode,low_consumption);

    czcv_camera::g_debug_roi = false;
    return 0;
}
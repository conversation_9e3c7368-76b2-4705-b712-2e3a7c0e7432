FILE(GLOB SRCS 
        src/base/*.cpp
        src/center_stage/*.cpp   
        src/config/*.cpp
        src/utils/*.cpp
        src/libopencl-stub/src/libopencl.cpp
        )

if(NOT WIN32)
    FILE(GLOB EXTRA_SRCS
        src/detector/*.cpp
        src/detector/detail/*.cpp
        src/tracker/*.cpp
        src/tracker/detail/*.cpp
        src/tracker/person_assert/*.cpp
        src/hand/rknn_yolov10.cpp
    )
    list(APPEND SRCS ${EXTRA_SRCS})
else()
    FILE(GLOB EXTRA_SRCS
        src/detector/base_detector.cpp
        src/detector/detector_factory.cpp
        src/tracker/base_tracker.cpp
        src/tracker/tracker_factory.cpp)
    list(APPEND SRCS ${EXTRA_SRCS})
endif()

include_directories(src/libopencl-stub/include)
if(BUILD_Shared)
    add_library(czcv_camera SHARED  ${SRCS})
    if(BUILD_ANDROID)
        if("${Dst_Platform}" STREQUAL android_v8a)
            target_compile_definitions(czcv_camera PRIVATE -DARM64 -D__ANDROID_ARM64__)
        else()
            target_compile_definitions(czcv_camera PRIVATE -DARM32 -D__ANDROID_ARM32__)
        endif()
    endif()
else()
    add_library(czcv_camera STATIC  ${SRCS} )
endif()

#
if(BUILD_ANDROID)
    target_link_libraries(czcv_camera ${RKNN_RT_LIB} ${GLOG_LIBS} -Wl,--whole-archive ${OpenCV_LIBS} ${TNN_LIBS} -Wl,--no-whole-archive -llog -lcutils -lbase -lc++ -lomp)
elseif(WIN32)
    target_link_libraries(czcv_camera ${GLOG_LIBS} ${OpenCV_LIBS} ${TNN_LIBS} ${NCNN_LIBS})
else()
    target_link_libraries(czcv_camera ${GLOG_LIBS} -Wl,--whole-archive ${OpenCV_LIBS} -Wl,--no-whole-archive -lpthread -fopenmp -lrt -ldl )
endif()

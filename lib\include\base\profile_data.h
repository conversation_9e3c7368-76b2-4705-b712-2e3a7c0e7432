// Copyright (C) 2020 CZUR Limited. All rights reserved.
// Only CZUR inner use.

#ifndef CZCV_CAMERA_PROFILE_DATA_H
#define CZCV_CAMERA_PROFILE_DATA_H

namespace  czcv_camera
{
    class ProfileData
    {
    private:
        float _fps; ///< process frame per seconds
    public:
        ProfileData(){_fps=0.0f;}
        float fps() const {return  _fps;}
        void  set_fps(float v, float  decay=0.4f)
        {
             _fps = (1-decay) * v +  decay * _fps;
        }
    };
}//namespace  czcv_camera

#endif //CZCV_CAMERA_PROFILE_DATA_H

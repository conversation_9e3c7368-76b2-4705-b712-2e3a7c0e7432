// Copyright (C) 2021 CZUR Limited. All rights reserved.
// Only CZUR inner use.

#ifndef CZCV_MOBILE_INTELLIGENCE_MULTI_BOX_TRACK_COMMON_H
#define CZCV_MOBILE_INTELLIGENCE_MULTI_BOX_TRACK_COMMON_H

#include "czcv_mobile/base/bbox.h"

namespace czcv_camera
{
    typedef  struct {
       BboxF  bboxF;
       float  iouScore;
       int missed;
       int detected;
       bool active;
    } BoxTrackInfo;

}

#endif //CZCV_MOBILE_INTELLIGENCE_MULTI_BOX_TRACK_COMMON_H

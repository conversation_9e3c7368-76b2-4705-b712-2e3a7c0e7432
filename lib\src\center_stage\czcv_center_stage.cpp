// Copyright (C) 2021 CZUR Limited. All rights reserved.
// Only CZUR inner use.
#ifdef __ANDROID__
#include <dlfcn.h>
#include <cutils/properties.h>
#endif //
#include "center_stage/czcv_center_stage.h"
#include "../utils/async_runner.h"
#include "../utils/hungarian_match.h"
#include "config/config_setter.h"
#include "base/common.h"
#include <opencv2/opencv.hpp>
#include <memory>
//#include "tracker/person_assert/person_assert.h"
#include "tracker/person_assert/rknn_person_assert.h"
//#include "hand/hand_gesture.h"
#include "hand/rknn_yolov10.h"
#include "detector/detect_white_board.h"

#include <libopencl.h>
#define DEBUG_MODE 0
#define DEBUG_SHOW_MODE 0

#define PersonAssert PersonAssertRKNN

#ifndef M_PI
    #define M_PI 3.14159265358979323846f
#endif

// int g_index = 0;
// int detect_size;
// std::string _mov = "";
// int detection_doa;
// int speak_size;
// int static_total;
// int filter_size;
// std::vector<int> new_id;
// std::vector<int> old_id;
// float iou;
// std::vector<czcv_camera::BboxF> debug_boxs;
#if DEBUG_SHOW_MODE
    std::vector<float> covered_doas_show;
    int detect_nums=0;
    bool state_show=false;
    int detection_doa;
    std::vector<int> speak_id_show;
    std::vector<int> id_show;
    std::vector<int> unmatched_track_id_show;
    std::vector<int> unmatched_static_id_show;
#endif

namespace czcv_camera
{
    typedef struct
    {
        BboxF box;
        bool bfocus = false;
        std::vector<timeval> time_list;
        float doa = -1;
    }static_boxs;

    typedef struct
    {
        float doa = -1;
        std::vector<timeval> time_list;
    }covered_doa;

    typedef struct
    {
        BboxF box;
        BboxF stride;
        int step;
        bool brunning = false;
        int missCount = 0;
        std::vector<cv::Rect> rects;
    }stWindowInfo;

    typedef struct
    {
        int _id;
        float _ymin;
    }reSetymin;

    typedef struct
    {
        int id;
        int frame_count=0;
        bool flag = false;
    }id_biClassification_infor;

    typedef struct
    {
        float doa;
        timeval time_stamp;
    }doa_infor;

    typedef enum
    {
        EN_GESTURE_MODE_NONE = 0x0,
        EN_GESTURE_MODE_TRACK = 0x1,
        EN_GESTURE_MODE_WHITEBOARD = 0x2,
        EN_GESTURE_MODE_MOVE_OR_ZOOM = 0x4,      
    }enGestureMode;

    typedef enum
    {
        EN_ZOOM_MOVE_STATE_STOP = 0,
        EN_ZOOM_MOVE_STATE_START = 1,
        EN_ZOOM_MOVE_STATE_RUNNING = 2,
        EN_ZOOM_MOVE_STATE_IDLE = 3,
    }enZoomMoveState;

    typedef enum
    {
        EN_MOVE_LEFT = 0,
        EN_MOVE_RIGHT = 1,
        EN_ZOOM_IN = 2,
        EN_ZOOM_OUT = 3,
        EN_NONE_TYPE = 0xFF,
    }enZoomMoveType;

    #define CZCV_GESTURE_PALM          (2) //20
    #define CZCV_GESTURE_STOP          (3)  //24
    #define CZCV_GESTURE_PEACE         (4)  //21
    #define CZCV_GESTURE_PEACE_INV     (5)  //22
    #define CZCV_GESTURE_THUMB_INDEX   (9999)//(6)  //31

    typedef enum
    {
        EN_WHITE_BOARD_STATE_NONE = 0,
        EN_WHITE_BOARD_STATE_RUNNING = 1,
        EN_WHITE_BOARD_STATE_SUCCESS = 2,
        EN_WHITE_BOARD_STATE_FAILED = 3,
        EN_WHITE_BOARD_STATE_START = 4,
        EN_WHITE_BOARD_STATE_STOP = 5,
    }enWhiteBoardState;
    
    enGestureMode gestureid2mode(int gestureid)
    {
        if (gestureid == CZCV_GESTURE_PALM || gestureid == CZCV_GESTURE_STOP)
        {
            return EN_GESTURE_MODE_TRACK;
        }
        else if (gestureid == CZCV_GESTURE_PEACE || gestureid == CZCV_GESTURE_PEACE_INV)
        {
            return EN_GESTURE_MODE_WHITEBOARD;
        }
        else if (gestureid == CZCV_GESTURE_THUMB_INDEX)
        {
            return EN_GESTURE_MODE_MOVE_OR_ZOOM;
        }
        else 
        {
            return EN_GESTURE_MODE_NONE;
        }
    }

    inline int clampInt(int v, int lo, int hi) {
        return std::max(lo, std::min(v, hi));
    }

    float centerSlowMapping(float t, float k=2.0f)
    {
        float u = 2.0 * t - 1.0;
        if (u == 0.0f) 
            return 0.5f;
        float sign = std::copysign(1.0f, u);
        float abs_u = std::fabs(u);
        return (sign * std::powf(abs_u, k) + 1.0f) / 2.0f;
    }

    class WindowUpdater
    {
    public:
        WindowUpdater()
        {
            _frameW = 1920;
            _frameH = 1080;
        }

        WindowUpdater(bool primary)
        {
            _primary = primary;
            _frameW = 1920;
            _frameH = 1080;
            longview = BboxF(1108, 702, 2574, 1542);
            if (_primary)
            {
                std::vector<cv::Point2f> src_pts, dst_pts;

                src_pts.push_back(cv::Point2f(2051,1433));
                src_pts.push_back(cv::Point2f(2122,1351));
                src_pts.push_back(cv::Point2f(1086,711));
                src_pts.push_back(cv::Point2f(1098,1519));
                src_pts.push_back(cv::Point2f(2541,730));
                src_pts.push_back(cv::Point2f(2553,1373));
                src_pts.push_back(cv::Point2f(1117,718));
                src_pts.push_back(cv::Point2f(1125,1526));
                src_pts.push_back(cv::Point2f(2590,706));
                dst_pts.push_back(cv::Point2f(2516,1875));
                dst_pts.push_back(cv::Point2f(2647,1665));
                dst_pts.push_back(cv::Point2f(24,20));
                dst_pts.push_back(cv::Point2f(20,2106));
                dst_pts.push_back(cv::Point2f(3753,75));
                dst_pts.push_back(cv::Point2f(3775,1723));
                dst_pts.push_back(cv::Point2f(22,39));
                dst_pts.push_back(cv::Point2f(44,2118));
                dst_pts.push_back(cv::Point2f(3816,14));

                _trans_short_to_long = cv::findHomography(src_pts, dst_pts, cv::LMEDS);
                _trans_long_to_short = cv::findHomography(dst_pts, src_pts, cv::LMEDS);
            }

            INV_SQRT_3 = 0.75355f;
            SQRT_3 = 1.327f; //tan(53)
            if (false == primary)
            {
                INV_SQRT_3 = 2.1445f;
                SQRT_3 = 0.4663f; //tan(25)
            }     

            _gesture_win.brunning = false;
            _gesture_win.missCount = 0;
            _gesture_win.step = 0;
            _gesture_win.box = BboxF(0, 0, 0, 0);

            _gesture_zoom_move_win.brunning = false;
            _gesture_zoom_move_win.missCount = 0;
            _gesture_zoom_move_win.step = 0;
            _gesture_zoom_move_win.box = BboxF(0, 0, 0, 0);  

            _last_win =  BboxF(0, 0, 0, 0);
            _last_move_zoom_win = BboxF(0, 0, 0, 0);     

            _zoom_move_win.clear();

            _last_hand_box.clear();
            _rgamat.handle = nullptr;
        }       

        void set_event(AsyncRunnerEvent e)
        {
            std::unique_lock<std::mutex> ulk(_mutEvent);
            _event = e;
        }
        AsyncRunnerEvent get_event()
        {
            std::unique_lock<std::mutex> ulk(_mutEvent);
            return  _event;
        }

        void stop()
        {
            bool brunning = true;
            set_event(STOP_LOOP);
            while(brunning)
            {
                std::unique_lock<std::mutex> ulk(_muWhiteBoardThread);
                brunning = _b_white_board_runnning;
                std::this_thread::sleep_for(std::chrono::milliseconds(5));
            }
              
            if (_rgamat.handle != NULL)
            {
                int ret = _rgaops->free(_rgamat.handle);
                if (ret != 0)
                {
                    LOGE("_rgaops->free failed: %d", ret);
                }
                _rgamat.handle = nullptr;
            }
            LOGE("WindowUpdater stop");
        }

        std::shared_ptr<rga_interface_t> _rgaops;
        float INV_SQRT_3;
        float SQRT_3;
        const float PI = 3.141592653f;

        BboxF _realbox;
        bool _longView = false;
        bool _longViewLast = false;
        bool _bViewChange = false;
        BboxF _longViewBox;     
        BboxF longview;
        int frame_count = 0;
        int frame_id = 0;
        int longview_frame_id = 0;
        std::vector<stWindowInfo> _winLongViewInfos;
        cv::Mat _trans_short_to_long;
        cv::Mat _trans_long_to_short;
        std::mutex _muLongView;
        bool _primary;
        timeval present_time={0,0};
        BboxF _fullbox;
        enGestureMode _gesture_mode = EN_GESTURE_MODE_NONE;
        int _instace_id = -1;
        stWindowInfo _gesture_win;
        int _tracked_instance_id = -1;
        bool _bwhite_board = false;
        BboxF _zoom_move_win;
        BboxF _last_hand_box;   //上一次处理的时候手的位置框（不是上一帧）
        stWindowInfo _gesture_zoom_move_win;
        bool _bmove_left_right = false;
        bool _bmove_left_right_accumulate = false;
        std::map<int, int> _det_id_map;
        BboxF _realtrackbox;

        enGestureMode g_debug_mode = EN_GESTURE_MODE_NONE;
        bool g_debug_ges_in_box = false;

        BboxF _last_win;
        BboxF _last_move_zoom_win;
        enZoomMoveState _zoom_move_state = EN_ZOOM_MOVE_STATE_STOP;
        enZoomMoveType _zoom_move_type = EN_NONE_TYPE;
        enGestureMode debug_gesturemode;
        enGestureEvent _gesture_event;
        bool _bfirst_move_zoom = true;
        bool _bborder_event_reported = false;
        std::thread _twhiteboard;
        struct timeval _last_lost_time;
        bool _blost = false;
        int _white_board_state = EN_WHITE_BOARD_STATE_NONE;
        BboxF _white_board_box;
        bool _white_board_long_view;
        std::mutex _muWhiteBoard, _muPhyAddr, _muWhiteBoardThread,_muPhyAddrSub,_mutEvent;
        bool _b_white_board_runnning = false;
        struct timeval _t_whiteboard_start;
        int _down_scale = 1;      
        int _phyaddr = -1;
        int _phyaddrsub = -1;
        czcv_camera::RgaMat _rgamat;
        czcv_camera::RgaMat _rgamatsub;
        czcv_camera::RgaMat _rgamatsubrgb;
        std::mutex _muRgaMat;
        std::vector<std::string> _modelPaths;
        AsyncRunnerEvent _event;

        void copy_to_rgamat(int phyaddr)
        {
            std::unique_lock<std::mutex> ulk(_muRgaMat);
            if (nullptr == _rgamat.handle)
            {
                _rgamat.handle = _rgaops->malloc_rga(&_rgamat.viraddr,&_rgamat.phyaddr, frame_w() / _down_scale, frame_h() / _down_scale, czcv_pixel_format_t::HAL_PIXEL_FORMAT_RGB_888);
                if (_rgamat.handle == NULL)
                {
                    LOGE("malloc_rga faild");
                    return;
                }
            }
            int ret = _rgaops->cropScaleRgb(phyaddr, _rgamat.phyaddr,frame_w() / _down_scale,frame_h() / _down_scale,0,0,frame_w() / _down_scale,frame_h() / _down_scale,frame_w() / _down_scale,frame_h() / _down_scale,0,0,frame_w() / _down_scale,frame_h() / _down_scale);	
            if (ret != 0)
            {
                LOGE("_rgaops cropScaleRgb failed: %d", ret);
            }
        }

        int phy_addr()
        {
            std::unique_lock<std::mutex> ulk(_muPhyAddr);        
            return _phyaddr;
        }

        void phy_addr(int phyaddr)
        {
            std::unique_lock<std::mutex> ulk(_muPhyAddr);  
            _phyaddr = phyaddr;
        }

        int phy_addr_sub()
        {
            std::unique_lock<std::mutex> ulk(_muPhyAddrSub);        
            return _phyaddrsub;
        }

        void phy_addr_sub(int phyaddr)
        {
            std::unique_lock<std::mutex> ulk(_muPhyAddrSub);  
            _phyaddrsub = phyaddr;
        }

        int n_frames_ = 180; //12s
        int _surveillance_cur_x = 0;
        int _surveillance_direction = 1;
        std::vector<BboxF> windows_;
        int cnt_{0};   

        std::vector<BboxF> genBBoxes(int y1, int y2) const 
        {
            std::vector<BboxF> out;
            out.reserve(n_frames_);

            const int h_start = static_cast<int>(std::round((2.0 / 3.0) * frame_h()));
            const int h_mid   = static_cast<int>(std::round((1.0 / 5.0) * frame_h()));

            // 宽高比固定（与 Python 逻辑一致，(2/3*w)/(2/3*h) 实际等同于 w/h）
            const double aspect_ratio = static_cast<double>(frame_w()) / static_cast<double>(frame_h());

            // 起止中心 X
            const double start_cx = (2.0 / 3.0 * frame_w()) / 2.0;        // w/3
            const double end_cx   = frame_w() - (2.0 / 3.0 * frame_w()) / 2.0;   // 2w/3
            const double center_y = (static_cast<double>(y1) + static_cast<double>(y2)) / 2.0;

            const int denom = std::max(1, n_frames_ - 1);

            for (int i = 0; i < n_frames_; ++i) {
                const double t = static_cast<double>(i) / static_cast<double>(denom); // [0,1]
                // 中间慢、两边快（默认 k=1.0 则为线性，可调大获得更“慢中间”）
                float s  = centerSlowMapping(t, 1.0);
                const double cx = start_cx + (end_cx - start_cx) * s;

                // 余弦平方曲线控制高度：t=0/1 为最大，t=0.5 为最小
                const double cosv   = std::cos(M_PI * t);
                double h_crop = static_cast<double>(h_mid) + (static_cast<double>(h_start) - static_cast<double>(h_mid)) * (cosv * cosv);

                // 宽度按比例
                const double w_crop = h_crop * aspect_ratio;

                int x1i = static_cast<int>(cx - w_crop / 2.0);
                int x2i = static_cast<int>(cx + w_crop / 2.0);

                // 竖向缩放到 2/3
                h_crop *= (2.0 / 3.0);

                int y1i = static_cast<int>(std::round(center_y - h_crop / 2.0)) - 100;
                int y2i = static_cast<int>(std::round(y1i + h_crop));

                // 边界裁剪
                x1i = clampInt(x1i, 0, frame_w() - 1);
                x2i = clampInt(x2i, 0, frame_w() - 1);
                y1i = clampInt(y1i, 0, frame_h() - 1);
                y2i = clampInt(y2i, 0, frame_h() - 1);

                out.push_back(BboxF{ (float)x1i, (float)y1i, (float)x2i, (float)y2i });
            }

            return out;
        }
        

        BboxF getNextWindow() {
            if (windows_.empty()) 
            {
                auto fwd = genBBoxes(0, frame_h() - 1);
                windows_.clear();
                windows_ = fwd;
                windows_.insert(windows_.end(), fwd.rbegin(), fwd.rend());
            }
            const int period = static_cast<int>(2) * static_cast<int>(n_frames_);
            const BboxF ret = windows_[static_cast<std::size_t>(cnt_ % period)];
            ++cnt_;
            return ret;
        }

        bool seekToBox(const BboxF& person_box) {
            if (windows_.empty()) return false;

            // 夹紧并保证坐标顺序
            int x1 = clampInt(person_box.xmin(), 0, frame_w() - 1);
            int y1 = clampInt(person_box.ymin(), 0, frame_h() - 1);
            int x2 = clampInt(person_box.xmax(), 0, frame_w() - 1);
            int y2 = clampInt(person_box.ymax(), 0, frame_h() - 1);
            if (x2 < x1) std::swap(x1, x2);
            if (y2 < y1) std::swap(y1, y2);
            BboxF pb{ (float)x1, (float)y1, (float)x2, (float)y2 };

            double best_iou = -1.0;
            std::size_t best_idx = 0;
            for (std::size_t i = 0; i < windows_.size(); ++i) {
                const double iou = pb.iou_with(windows_[i]);
                if (iou > best_iou) {
                    best_iou = iou;
                    best_idx = i;
                }
            }
            cnt_ = static_cast<int>(best_idx);
            return true;
        }

        void update_surveillance_window(std::vector<BboxF> & windows, TrackerInputOutput track)
        {      
            BboxF box = getNextWindow();

            // BboxF box;
            // box.xmin(x1);
            // box.xmax(x2);
            // box.ymin(y1);
            // box.ymax(y2);
            box.clip_by(0, frame_w() - 1, 0, frame_h() - 1);
            
            windows[0].xmin(box.xmin());
            windows[0].xmax(box.xmax());
            windows[0].ymin(box.ymin());
            windows[0].ymax(box.ymax());
        }

        void setGestureEvent(enGestureEvent event)
        {
            _gesture_event = event;
        }

        enGestureEvent getGestureEvent()
        {
            return _gesture_event;
        }

        void setWinLongViewInfo(stWindowInfo winInfo)
        {
            std::unique_lock<std::mutex> ulk(_muLongView);
            if (_winLongViewInfos.size() == 0)
            {
                _winLongViewInfos.push_back(winInfo);
            }
            else
            {
                _winLongViewInfos[0] = winInfo;
            }
            
        }

        bool getWinLongViewInfo(stWindowInfo &winInfo)
        {
            std::unique_lock<std::mutex> ulk(_muLongView);
            if(_winLongViewInfos.empty())
            {
                return false;
            }

            winInfo = _winLongViewInfos.front();
            return true;
        }
        
        Status process_window_by_doa(std::vector<static_boxs> &windows_s)
        {              
            float doa_now=doa();
            struct timeval StampDoa;
            gettimeofday(&StampDoa, NULL);
  
            if(doa_now!=-1)
            {
                int xcoor = (-tanf((doa_now - 270) * PI / 180) * INV_SQRT_3 * frame_w() * 0.5f + frame_w() * 0.5f);
                int minindex = -1;
                int minindex_out= -1;
                float distmin = frame_w();
                float distmin_out = frame_w();
                float anglemin = PI;
                float anglemin_out = PI;

                for (size_t i = 0; i < windows_s.size(); i++)
                {
                    auto box = windows_s[i].box;
                    float xc = (box.xmin() + box.xmax()) * 0.5f;

                    if (xcoor>box.xmin() && xcoor<box.xmax())
                    {
                        float distmin_temp=fabs(xc - xcoor);
                        if (distmin_temp < distmin)
                        {
                            distmin = distmin_temp;
                            minindex = i;
                            //anglemin = (-atanf(SQRT_3 * (xc - frame_w() * 0.5f) / (frame_w() * 0.5f))) * 180 / PI + 270;
                        }
                    }
                    else
                    {
                        float distmin_temp=fmin(fabs(box.xmin() - xcoor),fabs(box.xmax() - xcoor));
                        if (distmin_temp < distmin_out)
                        {
                            distmin_out = distmin_temp;
                            minindex_out = i;
                            anglemin_out = (-atanf(SQRT_3 * (xc - frame_w() * 0.5f) / (frame_w() * 0.5f))) * 180 / PI + 270;
                        }
                    }     
                }

                if (fabs(anglemin_out - doa_now) > 15) 
                {
                    minindex_out = -1;
                }

                if (minindex !=-1)
                {
                    windows_s[minindex].doa = doa_now;
                    if(windows_s[minindex].time_list.empty())
                    {
                        windows_s[minindex].time_list.push_back(StampDoa);
                        windows_s[minindex].time_list.push_back(StampDoa);
                    }
                    else
                    {
                        timeval last= windows_s[minindex].time_list[windows_s[minindex].time_list.size()-1];
                        double T_inter = 1000.0 * (StampDoa.tv_sec - last.tv_sec) + (StampDoa.tv_usec - last.tv_usec) / 1000.0;
                        if(T_inter>1000)
                        {
                            windows_s[minindex].time_list.push_back(StampDoa);
                            windows_s[minindex].time_list.push_back(StampDoa);
                        }
                        else
                        {
                            windows_s[minindex].time_list[windows_s[minindex].time_list.size()-1]=StampDoa;
                        }
                    }
                }
                else if(minindex_out != -1)
                {
                    windows_s[minindex_out].doa = doa_now;
                    if(windows_s[minindex_out].time_list.empty())
                    {
                        windows_s[minindex_out].time_list.push_back(StampDoa);
                        windows_s[minindex_out].time_list.push_back(StampDoa);
                    }
                    else
                    {
                        timeval last = windows_s[minindex_out].time_list[windows_s[minindex_out].time_list.size()-1];
                        double T_inter = 1000.0 * (StampDoa.tv_sec - last.tv_sec) + (StampDoa.tv_usec - last.tv_usec) / 1000.0;
                        if(T_inter>1000)
                        {
                            windows_s[minindex_out].time_list.push_back(StampDoa);
                            windows_s[minindex_out].time_list.push_back(StampDoa);
                        }
                        else
                        {
                            windows_s[minindex_out].time_list[windows_s[minindex_out].time_list.size()-1]=StampDoa;
                        }
                    }
                }
            }

            //删除5秒之前的信息
            for(auto &w:windows_s)
            {
                int t_len=w.time_list.size();
                int delect_num=0;
                for(int i=0;i<t_len/2;i++)
                {
                    double t_last = 1000.0 * (StampDoa.tv_sec - w.time_list[2*i+1].tv_sec) + (StampDoa.tv_usec - w.time_list[2*i+1].tv_usec) / 1000.0;
                    if(t_last>5000)
                    {
                        delect_num=2*(i+1);
                    }
                }
                if (delect_num!=0)
                {
                    w.time_list.erase(w.time_list.begin(),w.time_list.begin()+delect_num);
                }
            }
            //判断是否说话
            for(auto &w:windows_s)
            { 
                w.bfocus=false;
                int t_len=w.time_list.size();
                for(int i=0;i<t_len/2;i++)
                {
                    double t_last = 1000.0 * (w.time_list[2*i+1].tv_sec - w.time_list[2*i].tv_sec) + (w.time_list[2*i+1].tv_usec - w.time_list[2*i].tv_usec) / 1000.0;
                    if(t_last>=700)
                    {
                        w.bfocus=true;
                        break;
                    }
                }
            }
            return CZCV_OK;
        }

        Status process_windowCovered_by_doa(std::vector<BboxF> &windows)
        {
            float doa_now=doa();
            struct timeval StampDoa;
            gettimeofday(&StampDoa, NULL);

            if(doa_now!=-1)
            {
                if(windows.empty())
                {
                    bool find=false;
                    for(auto &infor_doa:_covered_doas)
                    {
                        if(doa_now==infor_doa.doa)
                        {
                            find=true;
                            if (infor_doa.time_list.empty())
                            {
                                infor_doa.time_list.push_back(StampDoa);
                                infor_doa.time_list.push_back(StampDoa);
                            }
                            else
                            {
                                timeval last=infor_doa.time_list[infor_doa.time_list.size()-1];
                                double T_inter = 1000.0 * (StampDoa.tv_sec - last.tv_sec) + (StampDoa.tv_usec - last.tv_usec) / 1000.0;
                                if(T_inter>1000)
                                {
                                    infor_doa.time_list.push_back(StampDoa);
                                    infor_doa.time_list.push_back(StampDoa);
                                }
                                else
                                {
                                    infor_doa.time_list[infor_doa.time_list.size()-1]=StampDoa;
                                }
                            }
                            break;
                        }
                    }

                    if(!find && doa_now!=-1)
                    {
                        std::vector<timeval> time_list;
                        time_list.push_back(StampDoa);
                        time_list.push_back(StampDoa);
                        _covered_doas.push_back({doa_now,time_list});
                    }
                }
                else
                {
                    int xcoor = (-tanf((doa_now- 270) * PI / 180) * INV_SQRT_3 * frame_w() * 0.5f + frame_w() * 0.5f);
                    int minindex = -1;
                    int minindex_out= -1;
                    float distmin = frame_w();
                    float distmin_out = frame_w();
                    float anglemin = PI;
                    float anglemin_out = PI;

                    for (size_t i = 0; i < windows.size(); i++)
                    {
                        auto box = windows[i];
                        float xc = (box.xmin() + box.xmax()) * 0.5f;

                        if (xcoor>box.xmin() && xcoor<box.xmax())
                        {
                            float distmin_temp=fabs(xc - xcoor);
                            if (distmin_temp < distmin)
                            {
                                distmin = distmin_temp;
                                minindex = i;
                                //anglemin = (-atanf(SQRT_3 * (xc - frame_w() * 0.5f) / (frame_w() * 0.5f))) * 180 / PI + 270;
                            }
                        }
                        else
                        {
                            float distmin_temp=fmin(fabs(box.xmin() - xcoor),fabs(box.xmax() - xcoor));
                            if (distmin_temp < distmin_out)
                            {
                                distmin_out = distmin_temp;
                                minindex_out = i;
                                anglemin_out = (-atanf(SQRT_3 * (xc - frame_w() * 0.5f) / (frame_w() * 0.5f))) * 180 / PI + 270;
                            }
                        }     
                    }

                    if (minindex ==-1 && fabs(anglemin_out-doa_now) > 15)
                    {
                        bool find=false;
                        for(auto &infor_doa:_covered_doas)
                        {
                            if(doa_now==infor_doa.doa)
                            {
                                find=true;
                                if (infor_doa.time_list.empty())
                                {
                                    infor_doa.time_list.push_back(StampDoa);
                                    infor_doa.time_list.push_back(StampDoa);
                                }
                                else
                                {
                                    timeval last=infor_doa.time_list[infor_doa.time_list.size()-1];
                                    double T_inter = 1000.0 * (StampDoa.tv_sec - last.tv_sec) + (StampDoa.tv_usec - last.tv_usec) / 1000.0;
                                    if(T_inter>1000)
                                    {
                                        infor_doa.time_list.push_back(StampDoa);
                                        infor_doa.time_list.push_back(StampDoa);
                                    }
                                    else
                                    {
                                        infor_doa.time_list[infor_doa.time_list.size()-1]=StampDoa;
                                    }
                                }
                                break;
                            }
                        }

                        if(!find && doa_now!=-1)
                        {
                            std::vector<timeval> time_list;
                            time_list.push_back(StampDoa);
                            time_list.push_back(StampDoa);
                            _covered_doas.push_back({doa_now,time_list});
                        }
                    }
                }
            }
            //删除5秒之前的信息
            for(auto &w:_covered_doas)
            {
                int t_len=w.time_list.size();
                int delect_num=0;
                for(int i=0;i<t_len/2;i++)
                {
                    double t_last = 1000.0 * (StampDoa.tv_sec - w.time_list[2*i+1].tv_sec) + (StampDoa.tv_usec - w.time_list[2*i+1].tv_usec) / 1000.0;
                    if(t_last>5000)
                    {
                        delect_num=2*(i+1);
                    }
                }
                if (delect_num!=0)
                {
                    w.time_list.erase(w.time_list.begin(),w.time_list.begin()+delect_num);
                }
            }
            //判断是否说话
            _doas.clear();
            for(auto &w:_covered_doas)
            { 
                int t_len=w.time_list.size();
                
                for(int i=0;i<t_len/2;i++)
                {
                    double t_last = 1000.0 * (w.time_list[2*i+1].tv_sec - w.time_list[2*i].tv_sec) + (w.time_list[2*i+1].tv_usec - w.time_list[2*i].tv_usec) / 1000.0;
                    if(t_last>=2000)
                    {
                        if(w.doa!=-1)
                        {
                            _doas.push_back(w.doa);
                        }
                        break;
                    }
                }
            }
            return CZCV_OK;
        }
        
        Status is_moving_cp(std::vector<BboxF> &WinsIn)
        {  
            if(WinsIn.size() == 0 || _dynamic_boxs.size() == 0)
            {
                // if(WinsIn.size() == 0 && _dynamic_boxs.size() != 0)
                // {
                    // gettimeofday(&_t_static_start, NULL);
                    // _dynamic_boxs.clear();
                // }
                if(WinsIn.size() != 0 && _dynamic_boxs.size() == 0)
                {
                    gettimeofday(&_t_static_start, NULL);
                    _dynamic_boxs.clear();
                    for(auto &box:WinsIn)
                    {
                        _dynamic_boxs.push_back(box);
                    }
                }
                else if(WinsIn.size() == 0 && _dynamic_boxs.size() == 0)
                {
                    _dynamic_boxs.clear();
                }
                return CZCV_OK;
            }

            /*
            开始
            这里是看2秒钟同一个id的doa差异，如果有差异过30度的认为在运动
            */
            struct timeval _last_thres;
            gettimeofday(&_last_thres, NULL);
            if (_first_thres)
            {
                gettimeofday(&_start_thres, NULL);
                _first_thres = false;
            }
            double t_interval = 1000.0 * (_last_thres.tv_sec - _start_thres.tv_sec) + (_last_thres.tv_usec - _start_thres.tv_usec) / 1000.0;
            if (t_interval > 2000)
            {
                gettimeofday(&_start_thres, NULL);
                _static_boxs_past.clear();
                for (auto _sboxs : _static_boxs)
                {
                    _static_boxs_past.push_back(_sboxs);
                }
            }
            
            for (auto _sboxs : _static_boxs)
            {
                for (auto _sboxs_p: _static_boxs_past)
                {
                    if (_sboxs.box.instance_id() == _sboxs_p.box.instance_id())
                    {
                        if(_sboxs.doa != -1 && _sboxs_p.doa != -1)
                        {
                            if (abs(_sboxs.doa - _sboxs_p.doa) >= 30)
                            {
                                gettimeofday(&_t_static_start, NULL);
                                return CZCV_OK;
                            }
                        }
                    }
                }
            }
            /*
            结束
            */

            /*
            开始
            运动状态下，判断是否有人持续移动
            */
            if (_is_moving)
            {
                if(_first_mov)
                {
                    _dynamic_boxs.clear();
                    for (auto s_box : WinsIn)
                    {
                        _dynamic_boxs.push_back(s_box);
                    }
                    _first_mov = false;
                }
                if (WinsIn.size() == _dynamic_boxs.size())
                {
                    for (auto box_now = WinsIn.begin(); box_now != WinsIn.end(); box_now++)
                    {
                        float max_score = 0.0f;
                        for (auto pre = _dynamic_boxs.begin(); pre != _dynamic_boxs.end(); pre++)
                        {
                            float score = (box_now->cv_rect() & pre->cv_rect()).area() * 1.0 / (box_now->cv_rect().area() + pre->cv_rect().area() - (box_now->cv_rect() & pre->cv_rect()).area());
                            if (score > max_score)
                            {
                                max_score = score;
                            }
                        }

                        if (max_score < 0.1)
                        {
                            gettimeofday(&_t_static_start, NULL);
                            _dynamic_boxs.clear();
                            for(auto &box:WinsIn)
                            {
                                _dynamic_boxs.push_back(box);
                            }
                            return CZCV_OK;
                        }
                    }

                    for (auto box_pre = _dynamic_boxs.begin(); box_pre != _dynamic_boxs.end(); box_pre++)
                    {
                        float max_score = 0.0f;
                        for (auto box_now = WinsIn.begin(); box_now != WinsIn.end(); box_now++)
                        {
                            float score = (box_pre->cv_rect() & box_now->cv_rect()).area() * 1.0 / (box_pre->cv_rect().area() + box_now->cv_rect().area() - (box_pre->cv_rect() & box_now->cv_rect()).area());
                            if (score > max_score)
                            {
                                max_score = score;
                            }
                        }
                    
                        if (max_score < 0.1)
                        {
                            gettimeofday(&_t_static_start, NULL);
                            _dynamic_boxs.clear();
                            for(auto &box:WinsIn)
                            {
                                _dynamic_boxs.push_back(box);
                            }
                            return CZCV_OK;
                        }
                    }
                }
            }
            /*
            结束
            */
            
            return CZCV_OK;
        }

        Status deal_box_for_static(BboxF &new_box,float iou_thre=0.6)
        { 
            float iou=_box_out.iou_with(new_box);
            if(iou>iou_thre)
            {
                new_box=_box_out;
            }
            else
            {
                _box_out=new_box;
            }
            return CZCV_OK;
        }

        //true代表未变化，false代表变化
        bool compare_doa_id(std::vector<int> &ids,std::vector<float> &doas)
        {
            //判断数量是否变化
            if(ids.size() != _doa_id.size() || doas.size() != _doa_covered.size())
            {
                return false;
            }
            //判断id是否变化
            std::vector<int> sortedid_present(ids);
            std::vector<int> sortedid_previous(_doa_id);

            std::sort(sortedid_present.begin(), sortedid_present.end());
            std::sort(sortedid_previous.begin(), sortedid_previous.end());

            for (size_t i = 0; i < sortedid_present.size(); ++i)
            {
                if (sortedid_present[i] != sortedid_previous[i]) 
                {
                    return false;
                }
            }

            //判断doa是否变化
            std::vector<float> sorteddoa_present(doas);
            std::vector<float> sorteddoa_previous(_doa_covered);

            std::sort(sorteddoa_present.begin(), sorteddoa_present.end());
            std::sort(sorteddoa_previous.begin(), sorteddoa_previous.end());

            for (size_t i = 0; i < sorteddoa_present.size(); ++i)
            {
                if (sorteddoa_present[i] != sorteddoa_previous[i]) 
                {
                    return false;
                }
            }
            return true;
        }

        void merge_box(std::vector<BboxF>::iterator boxiter,std::vector<BboxF>& windowsTra,BboxF& box)
        {
            for (boxiter = windowsTra.begin(); boxiter < windowsTra.end(); boxiter++)
            {
                box.xmin((std::min)(box.xmin(), boxiter->xmin()));
                box.xmax((std::max)(box.xmax(), boxiter->xmax()));
                box.ymin((std::min)(box.ymin(), boxiter->ymin()));
                box.ymax((std::max)(box.ymax(), boxiter->ymax()));
            }
            
            for(auto &doa:_doas)
            {
                float xmin=(-tanf((doa - 260) * PI / 180) * INV_SQRT_3 * frame_w() * 0.5f + frame_w() * 0.5f);
                float xmax=(-tanf((doa - 280) * PI / 180) * INV_SQRT_3 * frame_w() * 0.5f + frame_w() * 0.5f);
                box.xmin((std::min)(box.xmin(), xmin));
                box.xmax((std::max)(box.xmax(),xmax));
            }
            
            box.instance_id(0);
        }

        void detect_white_board(TrackerInputOutput  tracks, stGestureInfo gestureinfo)
        {         
            if (_rgamat.handle == nullptr)
            {
                LOGE("detect_white_board: _rgamat is nullptr");
                std::unique_lock<std::mutex> ulkthread(_muWhiteBoardThread);
                _b_white_board_runnning = false;
                
                std::unique_lock<std::mutex> ulk(_muWhiteBoard);
                _white_board_state = EN_WHITE_BOARD_STATE_FAILED;

                return;
            }

            LOGE("enter detect_white_board");

            cv::Mat bgr = cv::Mat(frame_h() / _down_scale, frame_w() / _down_scale, CV_8UC3, (void*)_rgamat.viraddr);

            cv::Mat image;
            {
                std::unique_lock<std::mutex> ulk(_muRgaMat);
                image = bgr.clone();
            }

            BboxF box = _fullbox;
            std::vector<BboxF> windowsIn = tracks.in_bbox();

            for (auto & track: windowsIn)
            {   
                if (track.class_id() != 0)
                {
                    continue;
                }
                int track_id = track.instance_id();
                if (_det_id_map.count(track_id) > 0)
                {
                    track_id = _det_id_map[track_id];
                }
                
                if (track_id == gestureinfo.instace_id)
                {
                    box = track;
                    break;
                }
            }
            std::shared_ptr<Detect_White_Board> detetor;
            detetor.reset(new Detect_White_Board());
            Status ret = detetor->init(_modelPaths);
            if (ret != CZCV_OK)
            {
                LOGE("detect_white_board detector init failed: %d", (int)ret);
            }

            _white_board_long_view = false;
            stWhiteBoardInfo info;            
            info.image = image;
            info.person_box.xmin(box.xmin() / _down_scale);
            info.person_box.xmax(box.xmax() / _down_scale);
            info.person_box.ymin(box.ymin() / _down_scale);
            info.person_box.ymax(box.ymax() / _down_scale);
            info.hand_box.xmin(gestureinfo.box.xmin() / _down_scale);
            info.hand_box.xmax(gestureinfo.box.xmax() / _down_scale);
            info.hand_box.ymin(gestureinfo.box.ymin() / _down_scale);
            info.hand_box.ymax(gestureinfo.box.ymax() / _down_scale);
     
            stWhiteBoardResult result;
            ret = detetor->run(info, result);
            if (ret != CZCV_OK)
            {
                LOGE("detect_white_board detector run failed: %d", (int)ret);
            }

            BboxF board_box = result.white_board_box;
            board_box.xmin( board_box.xmin() * _down_scale);
            board_box.xmax( board_box.xmax() * _down_scale);
            board_box.ymin( board_box.ymin() * _down_scale);
            board_box.ymax( board_box.ymax() * _down_scale);

            if (board_box.in(longview) && phy_addr_sub() != -1 && result.bfind)
            {
                _rgamatsub.handle = _rgaops->malloc_rga(&_rgamatsub.viraddr,&_rgamatsub.phyaddr, frame_w() / 2 / _down_scale, frame_h() / _down_scale, czcv_pixel_format_t::HAL_PIXEL_FORMAT_RGB_888);
                if (_rgamat.handle != NULL)
                {
                    int ret = _rgaops->cropScale(phy_addr_sub(), _rgamatsub.phyaddr,frame_w(),frame_h(),0,0,frame_w(),frame_h(),frame_w() / _down_scale,frame_h() / _down_scale);
                    if (ret != 0)
                    {
                        LOGE("RGA cropScale failed: %d", ret);                
                    }
                    _rgamatsubrgb.handle = _rgaops->malloc_rga(&_rgamatsubrgb.viraddr,&_rgamatsubrgb.phyaddr, frame_w() / _down_scale, frame_h() / _down_scale, czcv_pixel_format_t::HAL_PIXEL_FORMAT_RGB_888);
                    if (_rgamatsubrgb.handle != NULL)
                    {
                        int ret = _rgaops->yuv2rgb(_rgamatsub.phyaddr, _rgamatsubrgb.phyaddr, frame_w() / _down_scale, frame_h() / _down_scale, frame_w() / _down_scale, frame_h() / _down_scale);
                        if (ret != 0)
                        {
                            LOGE("RGA yuv2rgb failed: %d", ret);                
                        }

                        info.image = cv::Mat(frame_h() / _down_scale, frame_w() / _down_scale, CV_8UC3, (void*)_rgamatsubrgb.viraddr);
                        info.person_box.xmin(0);
                        info.person_box.xmax(frame_w() / _down_scale - 1);
                        info.person_box.ymin(0);
                        info.person_box.ymax(frame_h() / _down_scale - 1);
                        info.hand_box.xmin(0);
                        info.hand_box.xmax(frame_w() / _down_scale - 1);
                        info.hand_box.ymin(0);
                        info.hand_box.ymax(frame_h() / _down_scale - 1);

                        stWhiteBoardResult result_sub;
                        ret = detetor->run(info, result_sub);
                        if (ret != CZCV_OK)
                        {
                            LOGE("detect_white_board detector run sub failed: %d", (int)ret);
                        }

                        if (result_sub.bfind)
                        {
                            _white_board_long_view = true;
                            result = result_sub;
                        }           
                    }
                }
                
                if (_rgamatsub.handle != NULL)
                {
                    int ret = _rgaops->free(_rgamatsub.handle);
                    if (ret != 0)
                    {
                        LOGE("RGA free _rgamatsub failed: %d", ret);                
                    }
                    _rgamatsub.handle = nullptr;
                } 

                if (_rgamatsubrgb.handle != NULL)
                {
                    int ret = _rgaops->free(_rgamatsubrgb.handle);
                    if (ret != 0)
                    {
                        LOGE("RGA free _rgamatsubrgb failed: %d", ret);                
                    }
                    _rgamatsubrgb.handle = nullptr;
                } 
            }

            detetor->release();
            detetor.reset();

            AsyncRunnerEvent event = get_event();
            int sleep_time = 0;
            while(event != STOP_LOOP && sleep_time < 3000)
            {
                std::this_thread::sleep_for(std::chrono::milliseconds(10));
                sleep_time += 10;
                event = get_event();
            }
            
            if (event == STOP_LOOP)
            {
                std::unique_lock<std::mutex> ulkthread(_muWhiteBoardThread);
                _b_white_board_runnning = false;

                std::unique_lock<std::mutex> ulk(_muWhiteBoard);
                _white_board_state = EN_WHITE_BOARD_STATE_STOP;
                return;
            }

            std::unique_lock<std::mutex> ulkthread(_muWhiteBoardThread);
            _b_white_board_runnning = false;
            
            std::unique_lock<std::mutex> ulk(_muWhiteBoard);
            if (_white_board_state == EN_WHITE_BOARD_STATE_RUNNING)
            {
                if (result.bfind)
                {
                    _white_board_state = EN_WHITE_BOARD_STATE_SUCCESS;
                    _white_board_box.xmin(result.white_board_box.xmin() * _down_scale);
                    _white_board_box.xmax(result.white_board_box.xmax() * _down_scale);
                    _white_board_box.ymin(result.white_board_box.ymin() * _down_scale);
                    _white_board_box.ymax(result.white_board_box.ymax() * _down_scale);
                    BboxF & box = _white_board_box;
                    int outwidth = buffer_w();
                    int outheight = buffer_h();
                    float whratio = outwidth * 1.0f / outheight;
                    int neww = box.height() * whratio;
                    int newh = box.width() / whratio;
                            
                    if (neww > box.width())
                    {
                        int pad = (std::min)(frame_w() - 1 - box.xmin() - box.width(), box.xmin());
                        if (pad * 2 > (neww - box.width()))
                        {
                            box.xmin(box.xmin() - (neww - box.width()) / 2);
                            box.xmax(box.xmin() + neww);
                        }
                        else
                        {
                            int leftpad = (std::min)(neww - box.width() - pad, box.xmin());
                            int rightpad = (std::min)(neww - box.width() - pad, frame_w() - 1 - box.xmin() - box.width());
                            
                            float oldwidth = box.width();
                            box.xmin(box.xmin() - leftpad);
                            box.xmax(box.xmax() + rightpad);

                            if ((leftpad + rightpad) < (neww - oldwidth))
                            {
                                int newh = box.width() / whratio;
                                if (newh < box.height())
                                {
                                    float heightcrop = box.height() - newh;
                                    box.ymin(box.ymin() + heightcrop * 0.5f);
                                    box.ymax(box.ymax() - heightcrop * 0.5f);
                                }
                            }
                        }
                    }
                    if (newh > box.height())
                    {
                        int pad = (std::min)(frame_h() - 1 - box.ymax(), box.ymin());
                        if (pad * 2 > (newh - box.height()))
                        {
                            box.ymin(box.ymin() - (newh - box.height()) / 2);
                            box.ymax(box.ymin() + newh);
                        }
                        else
                        {
                            int toppad = (std::min)(newh - box.height() - pad, box.ymin());
                            int bottompad = (std::min)(newh - box.height() - pad, frame_h() - 1 - box.ymin() - box.height());
                            
                            float oldheight = box.height();
                            box.ymin(box.ymin() - toppad);
                            box.ymax(box.ymax() + bottompad);

                            if ((bottompad + toppad) < (newh - oldheight))
                            {
                                int neww = box.height() * whratio;
                                if (neww < box.width())
                                {
                                    float widthcrop = box.width() - neww;
                                    box.xmin(box.xmin() + widthcrop * 0.5f);
                                    box.xmax(box.xmax() - widthcrop * 0.5f);
                                }
                            }
                        }
                    }

                    _white_board_box = _white_board_box.clip_by(0, frame_w() - 1, 0, frame_h() - 1);
                }
                else
                {
                    _white_board_state = EN_WHITE_BOARD_STATE_FAILED;
                } 
            }
        }
        
        bool process_white_broad(TrackerInputOutput &tracks, std::vector<BboxF> &windowsOut, float& ratio, enWhiteBoardState state, stGestureInfo gestureinfo)
        {
            if (_primary == false)
            {
                return false;
            }

            bool ret = true;

            std::unique_lock<std::mutex> ulk(_muWhiteBoard);
            if (state == EN_WHITE_BOARD_STATE_START)
            {
                if (_white_board_state == EN_WHITE_BOARD_STATE_NONE)
                {
                    _white_board_state = EN_WHITE_BOARD_STATE_RUNNING;
                    std::unique_lock<std::mutex> ulk(_muWhiteBoardThread);
                    _b_white_board_runnning = true;
                    gettimeofday(&_t_whiteboard_start, NULL);
                    set_event(NORMAL_RUNNING);
                    _twhiteboard = std::thread(&WindowUpdater::detect_white_board, this, tracks, gestureinfo);
                    _twhiteboard.detach();                    
                }
            }
            else if (state == EN_WHITE_BOARD_STATE_RUNNING)
            {        
                if (_white_board_state == EN_WHITE_BOARD_STATE_RUNNING)
                {
                    return false;
                }
                else
                {
                    if (_white_board_state == EN_WHITE_BOARD_STATE_SUCCESS)
                    {
                        if (_white_board_long_view)
                        {
                            stWindowInfo winInfo;
                            winInfo.box = _white_board_box;
                            setWinLongViewInfo(winInfo); 
                            _longView = true;
                        }
                        else
                        {
                            _longView = false;
                            windowsOut.push_back(_white_board_box);
                        }

                        setGestureEvent(enGestureEventWhiteboardSuccess);
                    }
                    else if (_white_board_state == EN_WHITE_BOARD_STATE_FAILED)
                    {
                        setGestureEvent(enGestureEventWhiteboardFail);
                        ret = false;
                    }
                    else if (_white_board_state == EN_WHITE_BOARD_STATE_NONE)
                    {
                        if (_white_board_long_view)
                        {
                            stWindowInfo winInfo;
                            winInfo.box = _white_board_box;
                            setWinLongViewInfo(winInfo); 
                            _longView = true;
                        }
                        else
                        {
                            _longView = false;
                            windowsOut.push_back(_white_board_box);   
                        }     
                    }
                    else if (_white_board_state == EN_WHITE_BOARD_STATE_STOP)
                    {
                        setGestureEvent(enGestureEventWhiteboardStop);   
                        ret = false;
                    }
                    _white_board_state = EN_WHITE_BOARD_STATE_NONE;
                }
            }
            else
            {
                if (_white_board_state != EN_WHITE_BOARD_STATE_NONE)
                {
                    _white_board_state = EN_WHITE_BOARD_STATE_NONE;
                }
                ret = false;
            }

            return ret;
        }

        bool process_person_track(TrackerInputOutput &tracks, std::vector<BboxF> &windowsOut, int instace_id, bool bchange=false)
        {          
            if (instace_id != -1)
            {
                _tracked_instance_id = instace_id;
            }
            BboxF box = _fullbox;

            std::vector<BboxF> windowsIn = tracks.in_bbox();
            std::vector<BboxF> windowsTra = tracks.tracked_bbox();
            std::vector<BboxF> face_boxes(windowsIn.begin() + windowsTra.size(), windowsIn.end());
            bool bfind = false;
            // LOGE("primary: %d, instace_id: %d, _det_id_map", _primary, instace_id);
            // for (auto map: _det_id_map)
            // {
            //     LOGE("%d %d", map.first, map.second);
            // }
            for (auto & track: windowsTra)
            {              
                int track_id = track.instance_id();
                if (_det_id_map.count(track_id) > 0)
                {
                    track_id = _det_id_map[track_id];
                }
                
                if (track_id == _tracked_instance_id)
                {
                    box = track;
                    bfind = true;
                    break;
                }
            }

            if (bfind == false)
            {
                if (_blost == false)
                {
                    _blost = true;
                    gettimeofday(&_last_lost_time, NULL);
                }

                struct timeval t_now;
                gettimeofday(&t_now, NULL);
                double t_interval = 1000.0 * (t_now.tv_sec - _last_lost_time.tv_sec) + (t_now.tv_usec - _last_lost_time.tv_usec) / 1000.0;
                if (_primary)
                {
                    //LOGE("person track not found: %d, %d, %f, %f %f %f %f", _blost, _tracked_instance_id, t_interval, _gesture_win.box.xmin(), _gesture_win.box.xmax(), _gesture_win.box.ymin(), _gesture_win.box.ymax());
                }
                
                if (t_interval < 10000 && _gesture_win.box.area() > 1)
                {                 
                    windowsOut.push_back(_gesture_win.box);
                    return true;
                }
                else
                {
                    windowsOut.push_back(box);
                    return false;
                }   
            }
            _blost = false;

            update_height_by_face(face_boxes, box, windowsIn);
            _realtrackbox = box;
            //LOGE("track bbox: %f %f %f %f", box.xmin(), box.ymin(), box.xmax(), box.ymax());

            int outwidth = buffer_w();
            int outheight = buffer_h();
            float whratio = outwidth * 1.0f / outheight;
            _fullbox = BboxF(0.0f, 0.0f, _frameW - 1, _frameH - 1); 
            box = box.scale_by(_scale).clip_by(0, frame_w() - 1, 0, frame_h() - 1);        

            int min_height = input_h() * 0.5f;
            if (_primary)
            {
                min_height = input_h() / 3;
            }
            if (box.height() < min_height)
            {
                int pad = (std::min)(frame_h() - 1 - box.ymin() - box.height(), box.ymin());
                if (pad * 2 > (min_height - box.height()))
                {
                    box.ymin(box.ymin() - (min_height - box.height()) / 2);
                    box.ymax(box.ymin() + min_height);
                }
                else
                {
                    int toppad = (std::min)(min_height - box.height() - pad, box.ymin());
                    int bottompad = (std::min)(min_height - box.height() - pad, frame_h() - 1 - box.ymin() - box.height());
                    box.ymin(box.ymin() - toppad);
                    box.ymax(box.ymax() + bottompad);
                }
            }

            int neww = box.height() * whratio;
            int newh = box.width() / whratio;
                    
            if (neww > box.width())
            {
                int pad = (std::min)(frame_w() - 1 - box.xmin() - box.width(), box.xmin());
                if (pad * 2 > (neww - box.width()))
                {
                    box.xmin(box.xmin() - (neww - box.width()) / 2);
                    box.xmax(box.xmin() + neww);
                }
                else
                {
                    int leftpad = (std::min)(neww - box.width() - pad, box.xmin());
                    int rightpad = (std::min)(neww - box.width() - pad, frame_w() - 1 - box.xmin() - box.width());
                    
                    float oldwidth = box.width();
                    box.xmin(box.xmin() - leftpad);
                    box.xmax(box.xmax() + rightpad);

                    if ((leftpad + rightpad) < (neww - oldwidth))
                    {
                        int newh = box.width() / whratio;
                        if (newh < box.height())
                        {
                            float heightcrop = box.height() - newh;
                            box.ymin(box.ymin() + heightcrop * 0.5f);
                            box.ymax(box.ymax() - heightcrop * 0.5f);
                        }
                    }
                }
            }
            if (newh > box.height())
            {
                int pad = (std::min)(frame_h() - 1 - box.ymax(), box.ymin());
                if (pad * 2 > (newh - box.height()))
                {
                    box.ymin(box.ymin() - (newh - box.height()) / 2);
                    box.ymax(box.ymin() + newh);
                }
                else
                {
                    int toppad = (std::min)(newh - box.height() - pad, box.ymin());
                    int bottompad = (std::min)(newh - box.height() - pad, frame_h() - 1 - box.ymin() - box.height());
                    
                    float oldheight = box.height();
                    box.ymin(box.ymin() - toppad);
                    box.ymax(box.ymax() + bottompad);

                    if ((bottompad + toppad) < (newh - oldheight))
                    {
                        int neww = box.height() * whratio;
                        if (neww < box.width())
                        {
                            float widthcrop = box.width() - neww;
                            box.xmin(box.xmin() + widthcrop * 0.5f);
                            box.xmax(box.xmax() - widthcrop * 0.5f);
                        }
                    }
                }
            }
            //如果显示框接近全屏则显示全屏
            if (abs(box.width()-_frameW) < 20 || abs(box.height()-_frameH) < 20)
            {
                box=_fullbox;
            }
            box = box.clip_by(0, frame_w() - 1, 0, frame_h() - 1);
            
            _thres_iou = 0.65f;
            _step = 6;
            if (bchange)
            {
                _step = 1;
            }
            if (_gesture_win.box.xmax() < 0.5 || _gesture_win.box.ymax() < 0.5) 
            {
                _gesture_win.box = box;                               
                _gesture_win.brunning = false;
            }
            else
            {
                if (!_gesture_win.brunning)
                {                 
                    
                    if (box.iou_with(_gesture_win.box) > _thres_iou)
                    {
                        windowsOut.push_back(_gesture_win.box);
                        return true;
                    }
                    else
                    {
                        _gesture_win.step = _step;                               
                        _gesture_win.stride = (box - _gesture_win.box) / _gesture_win.step;
                        _gesture_win.brunning = true;
                    }                               
                }
                _gesture_win.box = _gesture_win.box + _gesture_win.stride;
                _gesture_win.step--;
                if (_gesture_win.step == 0)
                {
                    _gesture_win.brunning = false;
                }
            }
            
            windowsOut.push_back(_gesture_win.box);  

            return true;        
        }

        virtual enZoomMoveType judge_zoom_move_type(BboxF & cur_box, BboxF & last_box)
        {
            enZoomMoveType type = EN_NONE_TYPE;
            if (last_box.area() < 1)
            {
                last_box = cur_box;
            }

            if ((cur_box.xmax() < (last_box.xmax() - last_box.width() * 0.5f)) && cur_box.ymin() < last_box.ymax() && last_box.ymin() < last_box.ymax())       
            {               
                type = EN_MOVE_LEFT;            
            }    

            if ((cur_box.xmin() > (last_box.xmin() + last_box.width() * 0.5f)) && cur_box.ymin() < last_box.ymax() && last_box.ymin() < last_box.ymax())
            {
                //move right              
                type = EN_MOVE_RIGHT;
            }

            if (cur_box.ymax() < (last_box.ymax() - last_box.height() * 0.5f) && cur_box.xmin() < last_box.xmax() && last_box.xmin() < last_box.xmax())               
            {
                type = EN_ZOOM_IN;
            }

            if (cur_box.ymin() > (last_box.ymin() + last_box.height() * 0.5f) && cur_box.xmin() < last_box.xmax() && last_box.xmin() < last_box.xmax())
            {  
                type = EN_ZOOM_OUT;
            }

            return type;
        }

        void update_state_by_move_or_zoom_type(BboxF & stride, enZoomMoveType type)
        {
            float step = 0.002f;   
            BboxF box = _gesture_zoom_move_win.box;
            if (type == EN_MOVE_LEFT)       
            {    
                //move left          
                //stride = BboxF(-box.width() * step, 0, -box.width() * step, 0).clip_by(-box.xmin(), 0, 0, 0);
                stride = BboxF(-box.width() * step, 0, -box.width() * step, 0);
                _gesture_zoom_move_win.brunning = true;   
                _bmove_left_right = true;                                   
            }    

            else if (type == EN_MOVE_RIGHT)
            {
                //move right
                //stride = BboxF(box.width() * step, 0, box.width() * step, 0).clip_by(0, _frameW - 1 - box.xmax(), 0, 0);
                stride = BboxF(box.width() * step, 0, box.width() * step, 0);
                _gesture_zoom_move_win.brunning = true;
                _bmove_left_right = true;
            }

            else if (type == EN_ZOOM_IN)               
            {
                //zoom in
                float vertical_limit = (std::min)(box.ymin(), _frameH - 1 - box.ymax());
                float horizontal_limit = (std::min)(box.xmin(), _frameW - 1 - box.xmax());
                float whratio = (float)_frameW / _frameH;
                if (vertical_limit * whratio < horizontal_limit)
                {
                    horizontal_limit = vertical_limit * whratio;
                }
                else
                {
                    vertical_limit = horizontal_limit / whratio;
                }
                stride = BboxF(box.width() * step * 0.5f, box.height() * step * 0.5f, -box.width() * step * 0.5f, -box.height() * step * 0.5f);
                //stride = stride.clip_by(-horizontal_limit, horizontal_limit, -vertical_limit, vertical_limit);
                _gesture_zoom_move_win.brunning = true;
                _bmove_left_right = false;
            }

            else if (type == EN_ZOOM_OUT)
            {
                //zoom out
                float vertical_limit = (std::min)(box.ymin(), _frameH - 1 - box.ymax());
                float horizontal_limit = (std::min)(box.xmin(), _frameW - 1 - box.xmax());
                float whratio = (float)_frameW / _frameH;
                if (vertical_limit * whratio < horizontal_limit)
                {
                    horizontal_limit = vertical_limit * whratio;
                }
                else
                {
                    vertical_limit = horizontal_limit / whratio;
                }
                
                stride = BboxF(-box.width() * step * 0.5f, -box.height() * step * 0.5f, box.width() * step * 0.5f, box.height() * step * 0.5f);
                //stride = BboxF(-15, -15 * box.height() / box.width(), 15, 15 * box.height() / box.width());
                //stride = stride.clip_by(-horizontal_limit, horizontal_limit, -vertical_limit, vertical_limit);
                _gesture_zoom_move_win.brunning = true;
                _bmove_left_right = false;
            }

            // if (_primary && _gesture_zoom_move_win.brunning)
            // {
            //     LOGE("primary: %d, stride: %f %f %f %f", _primary, stride.xmin(), stride.ymin(), stride.xmax(), stride.ymax());
            // }
        }

        virtual void process_move_or_zoom(TrackerInputOutput &tracks, std::vector<BboxF> &windowsOut, stGestureInfo gestureinfo, enZoomMoveState state, enZoomMoveType type)
        {
            BboxF stride;
            if (_gesture_zoom_move_win.brunning == false)
            {           
                _bborder_event_reported = false;   
                //LOGE("1 primary: %d, _gesture_zoom_move_win.brunning: %d, type: %d\n", _primary, _gesture_zoom_move_win.brunning, type);
                update_state_by_move_or_zoom_type(stride, type);
                _zoom_move_type = type;
                if (type == EN_NONE_TYPE)
                {
                    _gesture_zoom_move_win.box = windowsOut[0];
                }

                if (_gesture_zoom_move_win.brunning)
                {
                    //LOGE("primary: %d, stride: %f %f %f %f", _primary, stride.xmin(), stride.ymin(), stride.xmax(), stride.ymax());
                    //_gesture_zoom_move_win.step = 10;                               
                    _gesture_zoom_move_win.stride = stride;
                    _last_hand_box = gestureinfo.box;    
                    _bmove_left_right_accumulate |= _bmove_left_right;  

                    if (_primary)
                    {
                        if (type == EN_MOVE_LEFT)  
                        {
                            setGestureEvent(enGestureEventMoveRight);  
                        }
                        else if (type == EN_MOVE_RIGHT)
                        {
                            setGestureEvent(enGestureEventMoveLeft); 
                        }
                        else if (type == EN_ZOOM_IN)
                        {
                            setGestureEvent(enGestureEventZoomIn); 
                        }
                        else if (type == EN_ZOOM_OUT)
                        {
                            setGestureEvent(enGestureEventZoomOut); 
                        }      
                    }                                                                     
                } 
            }
            else
            {
                //LOGE("2 primary: %d, _gesture_zoom_move_win.brunning: %d, type: %d\n", _primary, _gesture_zoom_move_win.brunning, type);
                if (type != _zoom_move_type && type != EN_NONE_TYPE)
                {
                    _bborder_event_reported = false;
                    _zoom_move_type = type;
                    update_state_by_move_or_zoom_type(stride, type);
                    if (_gesture_zoom_move_win.brunning)
                    {
                        //LOGE("stride: %f %f %f %f", stride.xmin(), stride.ymin(), stride.xmax(), stride.ymax());
                        //_gesture_zoom_move_win.step = 10;                               
                        _gesture_zoom_move_win.stride = stride;
                        _last_hand_box = gestureinfo.box;    
                        _bmove_left_right_accumulate |= _bmove_left_right; 

                        if (_primary)
                        {
                            if (type == EN_MOVE_LEFT)  
                            {
                                setGestureEvent(enGestureEventMoveRight);  
                            }
                            else if (type == EN_MOVE_RIGHT)
                            {
                                setGestureEvent(enGestureEventMoveLeft); 
                            }
                            else if (type == EN_ZOOM_IN)
                            {
                                setGestureEvent(enGestureEventZoomIn); 
                            }
                            else if (type == EN_ZOOM_OUT)
                            {
                                setGestureEvent(enGestureEventZoomOut); 
                            } 
                        }                                      
                    } 
                }
            }

            if (_gesture_zoom_move_win.brunning == true)
            {
                BboxF box = _gesture_zoom_move_win.box;
                box = box + _gesture_zoom_move_win.stride;
                //LOGE("4 primary: %d, %d, _gesture_zoom_move_win.box: %f %f %f %f",_primary,box.in(_fullbox),  _gesture_zoom_move_win.box.xmin(), _gesture_zoom_move_win.box.ymin(), _gesture_zoom_move_win.box.xmax(), _gesture_zoom_move_win.box.ymax());
                
                if (box.in(_fullbox) && box.height() >=  _fullbox.height() / 3)
                {        
                    _gesture_zoom_move_win.box = _gesture_zoom_move_win.box + _gesture_zoom_move_win.stride;
                    //LOGE("5 primary: %d %f %f %f %f", _primary, _gesture_zoom_move_win.box.xmin(), _gesture_zoom_move_win.box.ymin(), _gesture_zoom_move_win.box.xmax(), _gesture_zoom_move_win.box.ymax());
                }
                else
                {             
                    if (false == _bborder_event_reported)
                    {
                        if (_zoom_move_type == EN_MOVE_LEFT)  
                        {
                            setGestureEvent(enGestureEventRightBorder);  
                            _bborder_event_reported = true;
                        }
                        else if (_zoom_move_type == EN_MOVE_RIGHT)
                        {
                            setGestureEvent(enGestureEventLeftBorder); 
                            _bborder_event_reported = true;
                        }
                        else if (_zoom_move_type == EN_ZOOM_IN)
                        {
                            setGestureEvent(enGestureEventZoomInBorder); 
                            _bborder_event_reported = true;
                        }
                        else if (_zoom_move_type == EN_ZOOM_OUT)
                        {
                            setGestureEvent(enGestureEventZoomOutBorder);
                            _bborder_event_reported = true; 
                        } 
                    }     
                    
                }
                //LOGE("3 primary: %d, %d, _gesture_zoom_move_win.box: %f %f %f %f, %f %f %f %f",_primary,box.in(_fullbox), _gesture_zoom_move_win.box.xmin(), _gesture_zoom_move_win.box.ymin(), _gesture_zoom_move_win.box.xmax(), _gesture_zoom_move_win.box.ymax(), 
                 //       _fullbox.xmin(), _fullbox.ymin(), _fullbox.xmax(), _fullbox.ymax());

                if (_bmove_left_right)
                {
                    _zoom_move_win = _gesture_zoom_move_win.box;
                }  
            }

            windowsOut[0] = _gesture_zoom_move_win.box;
        }

        bool in_stationary_gesture_state()
        {
            std::unique_lock<std::mutex> ulk(_muWhiteBoard);
            if ( _gesture_mode == EN_GESTURE_MODE_WHITEBOARD && _white_board_state != EN_WHITE_BOARD_STATE_RUNNING) 
            {
                return true;
            }
            // //if (_zoom_move_state != EN_ZOOM_MOVE_STATE_STOP && _bmove_left_right_accumulate == true)
            // if (_bmove_left_right_accumulate == true)
            // {
            //     return true;
            // }
            return false;
        }
        
        virtual void update_move_zoom_state(TrackerInputOutput &tracks, enGestureMode gesturemode, BboxF& showwin, std::vector<BboxF> &windows, stGestureInfo gestureinfo, enZoomMoveType type, float scale_factor=1.2f)
        {
            if (_last_move_zoom_win.area() < 1)
            {
                _last_move_zoom_win = windows[0];
            }
            if (_last_win.area() < 1)
            {
                _last_win = windows[0];
            }

            BboxF expand_box = _last_move_zoom_win.scale_by(scale_factor);
            expand_box = expand_box.clip_by(0, _frameW - 1, 0, _frameH - 1);
            g_debug_ges_in_box = gestureinfo.box.in(expand_box);
            
            if (gesturemode == EN_GESTURE_MODE_MOVE_OR_ZOOM && gestureinfo.box.in(expand_box))
            //if (gesturemode == EN_GESTURE_MODE_MOVE_OR_ZOOM)
            {
                if (_bfirst_move_zoom && _primary)
                {
                    setGestureEvent(enGestureEventMoveZoom);
                    _bfirst_move_zoom = false;
                }
                
                if (_last_move_zoom_win.area() > 1)
                {
                    windows[0] = _last_move_zoom_win;
                }
                process_move_or_zoom(tracks, windows, gestureinfo, _zoom_move_state, type);
                _last_move_zoom_win = windows[0];
            }
            else
            {
                if ( _primary)
                {
                    _bfirst_move_zoom = true;
                }            
                
                _last_hand_box.clear();
                _gesture_zoom_move_win.brunning = false;
                _zoom_move_type = EN_NONE_TYPE;
                if (gesturemode == EN_GESTURE_MODE_TRACK || gesturemode == EN_GESTURE_MODE_WHITEBOARD)
                {
                    _bmove_left_right = false;
                    _bmove_left_right_accumulate = false;
                    _zoom_move_win.clear();
                    _last_move_zoom_win.clear();                
                }
                else
                {
                    //判断窗口是否有变化，不变则保持之前的移动窗口
                    if (_last_win.approximate(showwin, 0.5f))
                    {
                        windows[0] = _last_move_zoom_win;                       
                    }
                    else
                    {
                        //窗口有变化，恢复到左右平移最后的窗口
                        if (_bmove_left_right_accumulate)
                        {
                            _gesture_zoom_move_win.box = _zoom_move_win;
                            windows[0] = _zoom_move_win;
                            _last_move_zoom_win = windows[0];
                            showwin = windows[0];
                        }
                        else
                        {
                            _bmove_left_right = false;
                            _bmove_left_right_accumulate = false;
                            _zoom_move_win.clear();
                            _last_move_zoom_win.clear();
                        }
                    }     
                }
            }
        }

        virtual enGestureMode process_window_with_gesture(TrackerInputOutput &tracks, std::vector<BboxF> &windows, float& ratio, stGestureInfo gestureinfo)
        {
            setGestureEvent(enGestureEventNone);
            std::vector<BboxF> windows_gesture;
            bool bgesture_process = process_window_by_gesture(tracks, windows_gesture, ratio, gestureinfo);  
            //LOGE("process_window_by_gesture: bgesture_process: %d g_debug_mode: %d windowUpdater._gesture_mode: %d gestureinfo.bnew: %d gestureinfo.gesture_id: %d", bgesture_process, g_debug_mode, _gesture_mode, gestureinfo.bnew, gestureinfo.gesture_id);   
            if (true == bgesture_process)
            {
                if (windows_gesture.size() > 0)
                {
                    windows = windows_gesture;
                }            
            }
        
            enGestureMode gesturemode = gestureid2mode(gestureinfo.gesture_id);
            debug_gesturemode = gesturemode;
            BboxF showwin = windows[0];
                     
            //_zoom_move_type = EN_NONE_TYPE;
            enZoomMoveType type = judge_zoom_move_type(gestureinfo.box, _last_hand_box);         
            update_move_zoom_state(tracks, gesturemode, showwin, windows, gestureinfo, type);

            _last_win = showwin;

            return gesturemode;
        }


        void stop_person_track()
        {
            _gesture_win.box.clear();                               
            _gesture_win.brunning = false;
            _gesture_mode = EN_GESTURE_MODE_NONE;
            _instace_id = -1;
            setGestureEvent(enGestureEventPersonTrackStop); 
        }

        virtual bool process_window_by_gesture(TrackerInputOutput &tracks, std::vector<BboxF> &windowsOut, float& ratio, stGestureInfo gestureinfo)
        {
            enGestureMode mode = gestureid2mode(gestureinfo.gesture_id);
            g_debug_mode = mode;
            _bwhite_board = false;
            
            //不是第一次上报的手势
            if (gestureinfo.bnew == false)
            {
                if (_gesture_mode == EN_GESTURE_MODE_TRACK)
                {
                    bool bfind = process_person_track(tracks, windowsOut, _instace_id);
                    //LOGE("process_person_track 1: %f %f %f %f", windowsOut[0].xmin(), windowsOut[0].ymin(), windowsOut[0].xmax(), windowsOut[0].ymax());
                    if (bfind == false)
                    {                       
                        stop_person_track();
                    }
                    return bfind;
                }
                else if (_gesture_mode == EN_GESTURE_MODE_WHITEBOARD)
                {                  
                    _bwhite_board = true;
                    bool bfind = process_white_broad(tracks, windowsOut, ratio, EN_WHITE_BOARD_STATE_RUNNING, gestureinfo); 
                    if (bfind == false && _white_board_state != EN_WHITE_BOARD_STATE_RUNNING)
                    {                
                        _gesture_mode = EN_GESTURE_MODE_NONE;
                    }
                    return bfind;
                }
                return false;
            }
            //第一次上报
            else
            {            
                //新手势和之前上报的手势不一样
                if (mode != _gesture_mode)
                {                  
                    if (mode == EN_GESTURE_MODE_TRACK)
                    {
                        if (_gesture_mode == EN_GESTURE_MODE_WHITEBOARD)
                        {
                            bool bfind = process_white_broad(tracks, windowsOut, ratio, EN_WHITE_BOARD_STATE_STOP, gestureinfo);
                            // _gesture_mode = EN_GESTURE_MODE_NONE;                           
                            // setGestureEvent(enGestureEventWhiteboardStop);
                        }
                        bool bfind = process_person_track(tracks, windowsOut, gestureinfo.instace_id);
                        _gesture_mode = mode;
                        _instace_id = gestureinfo.instace_id;
                        setGestureEvent(enGestureEventPersonTrackStart);
                        return bfind;
                    }
                    else if (mode == EN_GESTURE_MODE_WHITEBOARD)
                    {
                        _bwhite_board = true;
                        _gesture_mode = mode;
                        bool bfind = process_white_broad(tracks, windowsOut, ratio, EN_WHITE_BOARD_STATE_START, gestureinfo);  
                        _instace_id = -1; 
                        setGestureEvent(enGestureEventWhiteboardStart);
                        return bfind;                
                    }
                    //新手势不是跟踪和白板手势，维持之前手势的处理
                    else
                    {
                        if (_gesture_mode == EN_GESTURE_MODE_TRACK)
                        {
                            bool bfind = process_person_track(tracks, windowsOut, _instace_id);
                            //LOGE("process_person_track 3: %f %f %f %f", windowsOut[0].xmin(), windowsOut[0].ymin(), windowsOut[0].xmax(), windowsOut[0].ymax());
                            //_instace_id = gestureinfo.instace_id;
                            if (bfind == false)
                            {
                                stop_person_track();
                            }
                            return bfind; 
                        }
                        else if (_gesture_mode == EN_GESTURE_MODE_WHITEBOARD)
                        {
                            _bwhite_board = true;
                            bool bfind = process_white_broad(tracks, windowsOut, ratio, EN_WHITE_BOARD_STATE_RUNNING, gestureinfo); 
                            _instace_id = -1;   
                            if (bfind == false)
                            {
                                _gesture_mode = EN_GESTURE_MODE_NONE;
                            }
                            return bfind;                 
                        }
                        else{
                            _instace_id = -1; 
                            return false;
                        }
                    }
                }
                else
                {                
                    if (mode == EN_GESTURE_MODE_TRACK)
                    {
                        if (_instace_id != gestureinfo.instace_id)
                        {
                            _gesture_mode = mode;
                            _instace_id = gestureinfo.instace_id;
                            bool bfind = process_person_track(tracks, windowsOut, _instace_id, true);   
                            setGestureEvent(enGestureEventPersonTrackStart);                     
                            return bfind;
                        }
                        else
                        {
                            stop_person_track();
                            return false;
                        }             
                    }
                    else if (mode == EN_GESTURE_MODE_WHITEBOARD)
                    {
                        struct timeval t_now;
                        gettimeofday(&t_now, NULL);
                        double t_interval = 1000.0 * (t_now.tv_sec - _t_whiteboard_start.tv_sec) + (t_now.tv_usec - _t_whiteboard_start.tv_usec) / 1000.0;
                        
                        bool bfind;
                        if (t_interval < 1000)
                        {
                            bfind = process_white_broad(tracks, windowsOut, ratio, EN_WHITE_BOARD_STATE_RUNNING, gestureinfo); 
                        }
                        else
                        {
                            bfind = process_white_broad(tracks, windowsOut, ratio, EN_WHITE_BOARD_STATE_STOP, gestureinfo); 
                            setGestureEvent(enGestureEventWhiteboardStop);
                            _gesture_mode = EN_GESTURE_MODE_NONE;                         
                        } 
                        _instace_id = -1; 
                        return bfind;
                    }
                    else 
                    {
                        _instace_id = -1; 
                        return false;
                    }
                }
            }

            _instace_id = -1; 
            return false;
        }

        void update_height_by_face(std::vector<BboxF> face_boxes, BboxF & trabox, std::vector<BboxF> windowsIn)
        {
            std::vector<BboxF>::iterator boxiter;

            for (boxiter = windowsIn.begin(); boxiter != windowsIn.end();boxiter++ )
            {
                if (boxiter->instance_id() == trabox.instance_id())
                {
                    break;
                }
            }
            if (boxiter == windowsIn.end())
            {
                for (boxiter = windowsIn.begin(); boxiter != windowsIn.end();boxiter++ )
                {
                    int mapidx = -1;
                    for(int n = 0;n < unmatch_static_id.size(); n++)
                    {
                        if (unmatch_static_id[n] == trabox.instance_id())
                        {
                            mapidx = n;
                            break;
                        }
                    }
                    if (mapidx != -1)
                    {
                        if (boxiter->instance_id() == unmatch_track_id[mapidx])
                        {
                            break;
                        }
                    }                              
                }
            }

            if (boxiter != windowsIn.end())
            {
                std::vector<BboxF> faces;
                for (auto & face: face_boxes)
                {
                    BboxF expandbox = boxiter->scale_by(1.1);
                    if (!face.in(expandbox))
                    {
                        continue;
                    }

                    float boxyc = (boxiter->ymin() + boxiter->ymax()) * 0.5f;
                    float faceyc = (face.ymin() + face.ymax()) * 0.5f;
                    if (faceyc > boxyc)
                    {
                        continue;
                    }

                    faces.push_back(face);                           
                }     

                if (faces.size() > 0)
                {
                    std::sort(faces.begin(), faces.end(), [](BboxF & b1, BboxF & b2) {return b1.ymin() + b1.ymax() < b2.ymin() + b2.ymax(); });
                    float ymax = (std::min)(trabox.ymax(), trabox.ymin() + faces[0].height() * 2.7f);
                    trabox.ymax(ymax);
                } 
            }
        }

        virtual Status process_window(TrackerInputOutput &tracks, std::vector<BboxF> &windowsOut, float& ratio, int Cmode)
        {
            int use_4k = get_use4k();
            
            std::vector<BboxF> windowsIn=tracks.in_bbox();
            std::vector<BboxF> windowsTra=tracks.tracked_bbox();
            int outwidth = buffer_w();
            int outheight = buffer_h();
            float whratio = outwidth * 1.0f / outheight;
            std::vector<BboxF> face_boxes(windowsIn.begin() + windowsTra.size(), windowsIn.end());

            windowsIn.erase(windowsIn.begin() + windowsTra.size(), windowsIn.end());
            _fullbox = BboxF(0.0f, 0.0f, _frameW - 1, _frameH - 1); 
            if (_frameW * 1.0 / _frameH > whratio)
            {
                int newhalfcrop = (_frameW - _frameH * whratio) / 2;
                _fullbox.xmin(_fullbox.xmin() + newhalfcrop);
                _fullbox.xmax(_fullbox.xmax() - newhalfcrop);
            }
            else
            {
                int newhalfcrop = (_frameH - _frameW / whratio) / 2;
                _fullbox.ymin(_fullbox.ymin() + newhalfcrop);
                _fullbox.ymax(_fullbox.ymax() - newhalfcrop);
            }

            if(Cmode == ModeVideoandAudio)
            {
#if DEBUG_SHOW_MODE
                speak_id_show.clear();
                detect_nums=windowsIn.size();
                covered_doas_show.clear();
                for(auto &d:_doas)
                {
                    covered_doas_show.push_back(d);
                }
#endif
                _step = 30 / 2;
                if (!_is_moving)
                {
                    process_windowCovered_by_doa(windowsIn);
                }
                
                if(_bfirst)
                {
                    gettimeofday(&_start, NULL);  
                    _bfirst=false;
                }
                struct timeval t_now;
                gettimeofday(&t_now, NULL);
                present_time=t_now;
                if(_Tlast<=0.0)
                {
                    _Tlast = 1000.0 * (t_now.tv_sec - _start.tv_sec) + (t_now.tv_usec - _start.tv_usec) / 1000.0;
                    windowsOut.clear();
                    windowsOut.push_back(_fullbox);
                    _t_static_start=t_now;
                    return CZCV_OK;
                }

                else if(_Tlast>=1000 && _Tlast<=4000)
                {
                    _Tlast = 1000.0 * (t_now.tv_sec - _start.tv_sec) + (t_now.tv_usec - _start.tv_usec) / 1000.0;
                    _doas.clear();
                }
                is_moving_cp(windowsIn);

                double T_static = 1000.0 * (t_now.tv_sec - _t_static_start.tv_sec) + (t_now.tv_usec - _t_static_start.tv_usec) / 1000.0;
                if(T_static<5000)
                {
                    std::vector<BboxF> _temp_filter;
                    BboxF box_(frame_w(), frame_h(), 0, 0);
                    for (auto tmp_box : windowsTra)
                    {
                        _temp_filter.push_back(tmp_box);
                        box_.xmin((std::min)(box_.xmin(), tmp_box.xmin()));
                        box_.xmax((std::max)(box_.xmax(), tmp_box.xmax()));
                    }
                    float temp_xmin = box_.xmin();
	                float temp_xmax = box_.xmax();
                    if (_num_dis < 100)
                    {
                        if (_temp_filter.size() < _dynamic_filter_boxs.size())
                        {
                            _num_dis += 1;
                            _is_missing = true;
                            _temp_filter.clear();
                            for (auto in_box : _dynamic_filter_boxs)
                            {
                                _temp_filter.push_back(in_box);
                            }
                            BboxF box_temp(frame_w(), frame_h(), 0, 0);
                            for (auto box : _temp_filter)
                            {
                                box_temp.xmin((std::min)(box_temp.xmin(), box.xmin()));
                                box_temp.xmax((std::max)(box_temp.xmax(), box.xmax()));
                            }
                            _xmin = box_temp.xmin();
                            _xmax = box_temp.xmax();
                            float sub_xmin = abs(temp_xmin - _xmin);
                            float sub_xmax = abs(temp_xmax - _xmax);
                            if (sub_xmin > sub_xmax)
                            {
                                _reset_xmin = true;
                            }
                            else
                            {
                                _reset_xmin = false;
                            }
                        }
                        else
                        {
                            _is_missing = false;
                            _num_dis = 0;
                        }
                    }
                    else
                    {
                        _is_missing = false;
                        _num_dis = 0;
                    }
                    _dynamic_filter_boxs.clear();
                    for (auto st_box : _temp_filter)
                    {
                        _dynamic_filter_boxs.push_back(st_box);
                    }
                    
                    if(_num_person == 0)
                    {
                        windowsTra.clear();
                        for(auto &b:_temp_filter)
                        {
                            windowsTra.push_back(b);
                        }
                    }

                    _is_moving = true;
                    _b_static_first=true;
                    if(_static2dynamic)
                    {
                        _step = 1;
                        _static2dynamic=false;
                    }
                    else
                    {
                        _step = 30 / 2;
                    }
                }
                else
                {
                    // _mov = "static!";
                    _first_mov = true;
                    _is_moving = false;
                    _static2dynamic=true;
                    _step = 15 / 2;
                    if(_b_static_first)
                    {
                        _static_boxs.clear();
                        _doa_id.clear();
                        for(auto &box:windowsTra)
                        {
                            std::vector<timeval> time_list;
                            _static_boxs.push_back(static_boxs{box,false,time_list,-1});
                            _doa_id.push_back(box.instance_id());
                        }
                        _b_static_first=false;
                        //当前时刻遮挡doa方向
                        _doa_covered.clear();
                        for(auto & d:_doas)
                        {
                            _doa_covered.push_back(d);
                        }
                    }
                    else
                    {
                        // new_id.clear();
                        // old_id.clear();
                        // for (auto box : windowsTra)
                        // {
                        //     new_id.push_back(box.instance_id());
                        // }
                        //位置更新
                        std::vector<int> id_matched;
                        for(auto &box_s:_static_boxs)
                        {
                            // old_id.push_back(box_s.box.instance_id());
                            for(auto &box_i :windowsTra)
                            {
                                if(box_s.box.instance_id()==box_i.instance_id())
                                {
                                    box_s.box=box_i;
                                    id_matched.push_back(box_i.instance_id());
                                }
                            }                       
                        }
                        //id匹配不到
                        std::vector<BboxF> _static_boxs_unmatched_boxs;
                        std::vector<int> _static_boxs_unmatched_ids;
                        for(auto &b:_static_boxs)
                        {
                            if (find(id_matched.begin(), id_matched.end(), b.box.instance_id()) == id_matched.end())
                            {
                                _static_boxs_unmatched_boxs.push_back(b.box);
                                _static_boxs_unmatched_ids.push_back(b.box.instance_id());
                            }
                        }
                        std::vector<BboxF> windowsTra_unmatched_boxs;
                        std::vector<int> windowsTra_unmatched_ids;
                        for(auto &b:windowsTra)
                        {
                            if (find(id_matched.begin(), id_matched.end(), b.instance_id()) == id_matched.end())
                            {
                                windowsTra_unmatched_boxs.push_back(b);
                                windowsTra_unmatched_ids.push_back(b.instance_id());
                            }
                        }
                        if (_static_boxs_unmatched_ids.size() == 0 && windowsTra_unmatched_ids.size() != 0)
                        {
                            for (auto tra : windowsTra)
                            {
                                for (auto unmatched_id : windowsTra_unmatched_ids)
                                {
                                    if (tra.instance_id() == unmatched_id)
                                    {
                                        std::vector<timeval> time_list;
                                        _static_boxs.push_back(static_boxs{tra,false,time_list,-1});
                                    }
                                }
                            }
                        }
                        if (_static_boxs_unmatched_ids.size() != 0 && windowsTra_unmatched_ids.size() == 0)
                        {
                            std::vector<static_boxs>::iterator boxiter_erase;
                            for (boxiter_erase = _static_boxs.begin(); boxiter_erase < _static_boxs.end();)
                            {
                                bool flag = true;
                                for (auto unmatched_id : _static_boxs_unmatched_ids)
                                {
                                    if (boxiter_erase->box.instance_id() == unmatched_id)
                                    {
                                        flag = false;
                                        boxiter_erase = _static_boxs.erase(boxiter_erase);
                                        break;
                                    }
                                }
                                if(flag)
                                {
                                    boxiter_erase++;
                                }
                            }
                        }
                        _det_id_map.clear();
                        //匈牙利匹配
                        if(windowsTra_unmatched_ids.size()!=0 && _static_boxs_unmatched_ids.size()!=0)
                        {
                            std::vector< std::vector<double> > costMatrix;
                            for(int i=0;i<_static_boxs_unmatched_boxs.size();i++)
                            {
                                std::vector<double> temp;
                                for(int j=0;j<windowsTra_unmatched_boxs.size();j++)
                                {
                                    temp.push_back(_static_boxs_unmatched_boxs[i].distance_with(windowsTra_unmatched_boxs[j]));
                                }
                                costMatrix.push_back(temp);
                            }
                        

                            HungarianAlgorithm HungAlgo;
                            std::vector<int> assignment;
                            std::vector<int> rematch_traid;
                            std::vector<int> rematch_staid;
                            double cost = HungAlgo.Solve(costMatrix, assignment);

                            //未匹配到的id位置更新
                            unmatch_static_id.clear();
                            unmatch_track_id.clear();
                            for (int i = 0; i < costMatrix.size(); ++i) 
                            {
                                int unmatched_static_id=_static_boxs_unmatched_ids[i];
                                int unmatched_windowsTra_id=windowsTra_unmatched_ids[assignment[i]];

                                unmatch_static_id.push_back(unmatched_static_id);
                                unmatch_track_id.push_back(unmatched_windowsTra_id);

                                for(auto &box_s:_static_boxs)
                                {
                                    if(box_s.box.instance_id()==unmatched_static_id)
                                    {
                                        for(auto &b:windowsTra_unmatched_boxs)
                                        {
                                            if(b.instance_id()==unmatched_windowsTra_id)
                                            {
                                                rematch_staid.push_back(unmatched_static_id);
                                                rematch_traid.push_back(unmatched_windowsTra_id);
                                                // b.instance_id(unmatched_static_id);
                                                box_s.box=b;
                                                _det_id_map[unmatched_windowsTra_id] = unmatched_static_id;
                                                break;
                                            }
                                        }
                                    }
                                }
                            }
                            if (rematch_traid.size() < windowsTra_unmatched_ids.size())
                            {
                                for (auto un_box : windowsTra_unmatched_boxs)
                                {
                                    std::vector<int>::iterator it = find(rematch_traid.begin(),rematch_traid.end(),un_box.instance_id());
                                    if (it != rematch_traid.end())
                                    {
                                        continue;
                                    }
                                    else
                                    {
                                        std::vector<timeval> time_list;
                                        _static_boxs.push_back(static_boxs{un_box,false,time_list,-1});
                                    }                 
                                }                         
                            }
                            
                            if (rematch_staid.size() < _static_boxs_unmatched_ids.size())
                            {
                                std::vector<static_boxs>::iterator boxiter_erase;
                                for (boxiter_erase = _static_boxs.begin(); boxiter_erase < _static_boxs.end();)
                                {
                                    std::vector<int>::iterator it = find(rematch_staid.begin(),rematch_staid.end(),boxiter_erase->box.instance_id());
                                    if (it != rematch_staid.end())
                                    {
                                        boxiter_erase++;
                                        continue;
                                    }
                                    else
                                    {
                                        boxiter_erase = _static_boxs.erase(boxiter_erase);
                                    }
                                }
                            }
#if DEBUG_SHOW_MODE
                            unmatched_static_id_show.clear();
                            unmatched_static_id_show=unmatch_static_id;
                            unmatched_track_id_show.clear();
                            unmatched_track_id_show=unmatch_track_id;
#endif                       
                         }
                        // static_total = _static_boxs.size();
                        //时间更新
                        process_window_by_doa(_static_boxs);
                        std::vector<static_boxs> temp;
                        _unspeak_person.clear();
                        for(auto &box_s:_static_boxs)
                        {
                            if(box_s.bfocus)
                            {
                                temp.push_back(box_s);
#if DEBUG_SHOW_MODE
                                speak_id_show.push_back(box_s.box.instance_id());
#endif
                            }
                            else
                            {
                                _unspeak_person.push_back(box_s);
                            }
                        }
                        /*
                        开始
                        这里看如果新增了一个说话的id，判断和之前的id差异是否超过10度
                        如果超过10度那就正常过去，如果不超过10度则删除新增的那个框
                        这段逻辑新增与否依赖于人的id
                        */
                        if (temp.size()!=0)
                        {
                            if (_first_erase)
                            {
                                _first_erase = false;
                            }
                            else
                            {
                                if (temp.size() > _speak_person.size())
                                {
                                    std::vector<int> _speak_person_id;
                                    for (auto sp : _speak_person)
                                    {
                                        _speak_person_id.push_back(sp.box.instance_id());
                                    }
                                    std::vector<static_boxs>::iterator boxiter_erase;
                                    for (boxiter_erase = temp.begin(); boxiter_erase != temp.end();)
                                    {
                                        std::vector<int>::iterator it = find(_speak_person_id.begin(),_speak_person_id.end(),boxiter_erase->box.instance_id());
                                        if (it != _speak_person_id.end())
                                        {
                                            boxiter_erase++;
                                            continue;
                                        }
                                        else
                                        {
                                            float add_erase = false;
                                            for (auto sp : _speak_person)
                                            {
                                                if (abs(boxiter_erase->doa - sp.doa) <= 10)
                                                {
                                                    add_erase = true;
                                                }
                                            }
                                            if(add_erase)
                                            {
                                                boxiter_erase = temp.erase(boxiter_erase);
                                            }
                                            else
                                            {
                                                boxiter_erase++;
                                            }
                                        }
                                    }
                                }
                            }
                            _speak_person.clear();
                            for(auto& box:temp)
                            {
                                _speak_person.push_back(box);
                            }
                        }
                        /*
                        结束
                        */
                        // speak_size = temp.size();
                        /*
                        找到所有人中y坐标最小的框，也就是离画面最近的那个框
                        因为不想远距离的人说话的时候把近距离的人放的特别特别大
                        所以远距离的人显示的y坐标要换成近距离的y坐标去放大
                        */
                        if(temp.size()!=0)
                        {
                            _speaking = true;
                            _static_boxs_filter.clear();
                            _step = 1;
                            windowsTra.clear();
                            for(auto& box:temp)
                            {
                                windowsTra.push_back(box.box);
                            }
                        }
      
                        else
                        {
                            _num_person = _static_boxs.size();
                            _first_erase = true;
                            _speak_person.clear();
                            _speaking = false;
                            std::vector<static_boxs> _temp_filter;
                            BboxF box_(frame_w(), frame_h(), 0, 0);
                            for (auto tmp_box : _static_boxs)
                            {
                                _temp_filter.push_back(tmp_box);
                                box_.xmin((std::min)(box_.xmin(), tmp_box.box.xmin()));
                                box_.xmax((std::max)(box_.xmax(), tmp_box.box.xmax()));
                            }
                            float temp_xmin = box_.xmin();
                            float temp_xmax = box_.xmax();
                            if (_num_dis < 100)
                            {
                                // if (_temp_filter.size() < _static_boxs_filter.size() && _temp_filter.size() != 0)
                                if (_temp_filter.size() < _static_boxs_filter.size())
                                {
                                    _num_dis += 1;
                                    _is_missing = true;
                                    _temp_filter.clear();
                                    for (auto in_box : _static_boxs_filter)
                                    {
                                        _temp_filter.push_back(in_box);
                                    }
                                    BboxF box_temp(frame_w(), frame_h(), 0, 0);
                                    for (auto box : _temp_filter)
                                    {
                                        box_temp.xmin((std::min)(box_temp.xmin(), box.box.xmin()));
                                        box_temp.xmax((std::max)(box_temp.xmax(), box.box.xmax()));
                                    }
                                    _xmin = box_temp.xmin();
                                    _xmax = box_temp.xmax();
                                    float sub_xmin = abs(temp_xmin - _xmin);
                                    float sub_xmax = abs(temp_xmax - _xmax);
                                    if (sub_xmin > sub_xmax)
                                    {
                                        _reset_xmin = true;
                                    }
                                    else
                                    {
                                        _reset_xmin = false;
                                    }
                                }
                                else
                                {
                                    _is_missing = false;
                                    _num_dis = 0;
                                }
                            }
                            else
                            {
                                _is_missing = false;
                                _num_dis = 0;
                            }
                            _static_boxs_filter.clear();
                            for (auto st_box : _temp_filter)
                            {
                                _static_boxs_filter.push_back(st_box);
                            }

                            windowsTra.clear();
                            if (_num_person > 0)
                            {
                                for(auto &b:_static_boxs)
                                {
                                    windowsTra.push_back(b.box);
                                }
                            }
                            else
                            {
                                for(auto &b:_temp_filter)
                                {
                                    windowsTra.push_back(b.box);
                                }
                            }
                        }
                    }
                }


#if DEBUG_SHOW_MODE                
                id_show.clear();
                for(auto t:windowsTra)
                {
                    id_show.push_back(t.instance_id());
                }
                state_show=_static2dynamic;
#endif
                std::vector<BboxF>::iterator boxiter;

                if (_static2dynamic)
                {
                    for (auto &box: windowsTra)
                    {
                        update_height_by_face(face_boxes, box, windowsIn);
                    }
                }

                for (boxiter = windowsTra.begin(); boxiter < windowsTra.end(); boxiter++)
                {
                    *boxiter = boxiter->clip_by(0, frame_w() - 1, 0, frame_h() - 1);
                }

                BboxF realbox(-1, -1, -1, -1);
                
                int outwidth = buffer_w();
                int outheight = buffer_h();
                
                BboxF box(frame_w(), frame_h(), 0, 0);
                if (windowsTra.size() == 0)
                {
                    box = _fullbox;
                    box.instance_id(0);
                }
                else
                {
                    merge_box(boxiter,windowsTra,box);
                    if (_speaking)
                    {
                        for (auto &t : windowsTra)
                        {
                            _speaking_ids.push_back(t.instance_id());
                        }
                    }
                    if (_is_missing)
                    {
                        if (_reset_xmin)
                        {
                            box.xmin(_xmin);
                        }
                        else
                        {
                            box.xmax(_xmax);
                        }
                    }
                    if (_speaking)
                    {
                        float _ymin = 100000.f;
                        bool resetymin = false;
                        for (auto us_person : _unspeak_person)
                        {
                            float score = (us_person.box.cv_rect() & box.cv_rect()).area() * 1.0 / (us_person.box.cv_rect().area() + box.cv_rect().area() - (us_person.box.cv_rect() & box.cv_rect()).area());
                            if (score > 0.5)
                            {
                                if (us_person.box.ymin() < _ymin && us_person.box.ymin() < box.ymin())
                                {
                                    _ymin = us_person.box.ymin();
                                    resetymin = true;
                                }
                            }
                        }
                        if (resetymin)
                        {
                            box.ymin(_ymin);
                        }
                    }
                    if(!_speaking && _num_person > 0)
                    {
                        if(_first_copy)
                        {
                            _resetymin = box.ymin();
                            _resetymax = box.ymax();
                            _first_copy = false;
                        }
                        else
                        {
                            float ratio_ymin = std::min(_resetymin,box.ymin()) / std::max(_resetymin,box.ymin());
                            float ratio_ymax = std::min(_resetymax,box.ymax()) / std::max(_resetymax,box.ymax());
                            if(ratio_ymin > 0.95f) //0.8
                            {
                                box.ymin(_resetymin);
                                box.ymax(_resetymax);
                            }
                            else
                            {
                                _resetymin = box.ymin();
                                _resetymax = box.ymax();
                            }
                            // if(ratio_ymax < 0.2)
                            // {
                            //     box.ymax(_resetymax);
                            // }
                            // else
                            // {
                            //     _resetymax = box.ymax();
                            // }
                        }
                    }
                    else
                    {
                        _first_copy = true;                        
                    }
                }

                realbox = box;
                _realbox = realbox;
                box = box.scale_by(_scale).clip_by(0, frame_w() - 1, 0, frame_h() - 1);
                
                int min_height;
                if (!use_4k)
                {
                    min_height = input_h() * 2.0 / 4;
                    if (_primary)
                    {
                        min_height = input_h() / 3;
                    }
                    // min_height = input_h() * 0.3;
                }
                else
                {
                    min_height = input_h()  / 3;
                }
                if (box.height() < min_height)
                {
                    int pad = (std::min)(frame_h() - 1 - box.ymin() - box.height(), box.ymin());
                    if (pad * 2 > (min_height - box.height()))
                    {
                        box.ymin(box.ymin() - (min_height - box.height()) / 2);
                        box.ymax(box.ymin() + min_height);
                    }
                    else
                    {
                        int toppad = (std::min)(min_height - box.height() - pad, box.ymin());
                        int bottompad = (std::min)(min_height - box.height() - pad, frame_h() - 1 - box.ymin() - box.height());
                        box.ymin(box.ymin() - toppad);
                        box.ymax(box.ymax() + bottompad);
                    }
                }

                int neww = box.height() * whratio;
                int newh = box.width() / whratio;
                        
                if (neww > box.width())
                {
                    int pad = (std::min)(frame_w() - 1 - box.xmin() - box.width(), box.xmin());
                    if (pad * 2 > (neww - box.width()))
                    {
                        box.xmin(box.xmin() - (neww - box.width()) / 2);
                        box.xmax(box.xmin() + neww);
                    }
                    else
                    {
                        int leftpad = (std::min)(neww - box.width() - pad, box.xmin());
                        int rightpad = (std::min)(neww - box.width() - pad, frame_w() - 1 - box.xmin() - box.width());
                        
                        float oldwidth = box.width();
                        box.xmin(box.xmin() - leftpad);
                        box.xmax(box.xmax() + rightpad);

                        if ((leftpad + rightpad) < (neww - oldwidth))
                        {
                            int newh = box.width() / whratio;
                            if (newh < box.height())
                            {
                                float heightcrop = box.height() - newh;
                                box.ymin(box.ymin() + heightcrop * 0.5f);
                                box.ymax(box.ymax() - heightcrop * 0.5f);
                            }
                        }
                    }
                }
                if (newh > box.height())
                {
                    int pad = (std::min)(frame_h() - 1 - box.ymax(), box.ymin());
                    if (pad * 2 > (newh - box.height()))
                    {
                        box.ymin(box.ymin() - (newh - box.height()) / 2);
                        box.ymax(box.ymin() + newh);
                    }
                    else
                    {
                        int toppad = (std::min)(newh - box.height() - pad, box.ymin());
                        int bottompad = (std::min)(newh - box.height() - pad, frame_h() - 1 - box.ymin() - box.height());
                        
                        float oldheight = box.height();
                        box.ymin(box.ymin() - toppad);
                        box.ymax(box.ymax() + bottompad);

                        if ((bottompad + toppad) < (newh - oldheight))
                        {
                            int neww = box.height() * whratio;
                            if (neww < box.width())
                            {
                                float widthcrop = box.width() - neww;
                                box.xmin(box.xmin() + widthcrop * 0.5f);
                                box.xmax(box.xmax() - widthcrop * 0.5f);
                            }
                        }
                    }
                }
                //如果显示框接近全屏则显示全屏
                if (abs(box.width()-_frameW)<20 || abs(box.height()-_frameH)<20)
                {
                    box=_fullbox;
                }
                box = box.clip_by(0, frame_w() - 1, 0, frame_h() - 1);
                windowsTra.clear();
                windowsTra.push_back(box);
                if (_first_process)
                {
                    box = _fullbox;
                    windowsTra.clear();
                    windowsTra.push_back(box);
                }

                if (!use_4k)
                {
                    _thres_iou = 0.75f; //0.65f
                }
                else
                {
                    
                    if (_speaking)
                    {
                        _thres_iou = 0.5f;
                    }
                    else
                    {
                        if (_num_person == 1)
                        {
                            _thres_iou = 0.75f;
                        }
                        else
                        {
                            _thres_iou = 0.75f;
                        }
                    }
                }

                get_output_window(box, windowsOut);
                return CZCV_OK;
            }
            return CZCV_OK;
        }

        void get_output_window(BboxF box, std::vector<BboxF> &windowsOut, bool btrack=false)
        {
            std::vector<BboxF>::iterator boxiter;
            std::vector<BboxF> windowsTra;
            windowsTra.push_back(box);

            std::vector<stWindowInfo> & winInfos = btrack ? _winInfos_track : _winInfos;
            
            std::vector<stWindowInfo>::iterator iter;
            for (iter = winInfos.begin(); iter < winInfos.end();)
            {
                bool bfind = false;
                _first_process = false;
                for (boxiter = windowsTra.begin(); boxiter < windowsTra.end();boxiter++)
                {
                    if(iter->box.instance_id() == boxiter->instance_id())
                    {
                        bfind = true;
                        iter->missCount = 0;
                        
                        if (!iter->brunning)
                        {                 
                            // BboxF expand_box = iter->box.scale_by(1.05).clip_by(0, frame_w() - 1, 0, frame_h() - 1);
                            // LOGE("[WZW LOG] (*boxiter).iou_with(iter->box): %f",(*boxiter).iou_with(iter->box));
                            // if ((realbox).in(expand_box) && (*boxiter).iou_with(iter->box) > _thres_iou)
                            if ((*boxiter).iou_with(iter->box) > _thres_iou)
                            {
                                boxiter = windowsTra.erase(boxiter);
                                break;
                            }
                            else
                            {
                                // if (is_speaking ())
                                // {
                                //     if ((*boxiter).iou_with(iter->box) > 0.1f)
                                //     {
                                //         _step = 30 / 2;
                                //     }
                                //     else
                                //     {
                                //         _step = 1;
                                //     }                            
                                // }
                                iter->step = _step;                               
                                iter->stride = ((*boxiter) - iter->box) / iter->step;
                                iter->brunning = true;
                            }                               
                        }
                        iter->box = iter->box + iter->stride;
                        iter->step--;
                        if (iter->step == 0)
                        {
                            iter->brunning = false;
                        }
                        boxiter = windowsTra.erase(boxiter);
                        break;
                    }
                }
                if (!bfind)
                {
                    iter->missCount++;                       
                }

                if (iter->missCount > _win_missing_thr) //连续丢失60帧，则删掉
                {
                    iter = winInfos.erase(iter);
                    continue;
                }
                iter++;
            }
            for (boxiter = windowsTra.begin(); boxiter < windowsTra.end();boxiter++)
            {
                stWindowInfo info;
                info.box = (*boxiter);
                info.missCount = 0;
                info.brunning = false;
                info.step = 0;
                winInfos.push_back(info);              
            }
            
            for(auto info: winInfos)
            {
                //deal_box_for_static(info.box);
                windowsOut.push_back(info.box);
            }
            
            if(windowsOut.empty())
            {
                //deal_box_for_static(fullbox);
                windowsOut.push_back(_fullbox);
            }
        }
    
        void camera_change_process()
        {
            //char device_model[PROPERTY_VALUE_MAX] = {0};
            //property_get("persist.vendor.czur.hardware.model", device_model, "");
            
            bool device_studio = false;// strcasecmp(device_model, "studio") == 0;
            if (_primary && device_studio == false)
            {
                _longView = false;
                frame_count++;

                BboxF expand_box = longview;
                BboxF realbox = _realbox;
                
                if ((realbox.in(expand_box) && ((_longViewLast == false && (frame_count - longview_frame_id) >= 15) || _longViewLast)) ||
                    (realbox.in(expand_box) == false && _longViewLast == true && (frame_count - frame_id) < 15))
                {
                    realbox = realbox.scale_by(1.1f);
                    realbox = realbox.clip_by(longview.xmin(), longview.xmax(), longview.ymin(), longview.ymax());
                    _longView = true;
                    if (_longView != _longViewLast)
                    {
                        _step = 1;
                        _longViewLast = _longView;   
                        _bViewChange = true;
                    }

                    longview_frame_id = frame_count;                                
                }
                else
                {
                    if (_longView != _longViewLast)
                    {
                        _step = 1;
                        _longViewLast = _longView;   
                        _bViewChange = true;
                    }    
                    frame_id = frame_count;
                }
            }
        }

        void set_use4k(int use_4k){_use_4k = use_4k;}
        void frame_w(int w){_frameW = w;}
        void frame_h(int h){_frameH = h;}
        int frame_w() const {return  _frameW;}
        int frame_h() const {return  _frameH;}
        int get_use4k(){return _use_4k;}

        void buffer_w(int w) {_bufferW = w;}
        void buffer_h(int h) {_bufferH = h;}
        int buffer_w() const {return _bufferW;}
        int buffer_h() const {return _bufferH;}

        void input_w(int w) {_input_w = w;}
        void input_h(int h) {_input_h = h;}
        int input_w() const {return _input_w;}
        int input_h() const {return _input_h;}


        void doa(float doa) {_doa = doa;};
        float doa() {return _doa;};

        enum state
        {
            en_STATE_DOA,
            en_STATE_VISION,
        };

        bool is_moving() {return _is_moving;}
        bool is_speaking()
        {
            return _speaking;
        }
        std::vector<int> _speaking_ids;

    protected:
        bool _first_process = true;


        int _frameW, _frameH;
        int _bufferW, _bufferH;
        int _input_w, _input_h;
        std::vector<stWindowInfo> _winInfos;
        std::vector<stWindowInfo> _winInfos_track;
        
        int _step = 30 / 2;
        float _scale = 1.2f;
        const int _win_missing_thr = 30;
        float _doa = -1;
        int _num_dis = 0;
        int _use_4k;
        float _thres_iou;
        int _num_person;
        float _xmin,_xmax;
        float _resetymin,_resetymax;

        state _currentState = en_STATE_VISION;

        std::vector<static_boxs> _static_boxs;
        std::vector<static_boxs> _static_boxs_past;
        std::vector<static_boxs> _speak_person;
        std::vector<static_boxs> _static_boxs_filter;
        std::vector<static_boxs> _unspeak_person;
        //当前时刻被遮挡doa方向
        std::vector<covered_doa> _covered_doas;
        //当前时刻应该显示的doa方向
        std::vector<float> _doas;

        std::vector<int> unmatch_track_id;
        std::vector<int> unmatch_static_id;
        std::vector<reSetymin> reset_ymin_id;

        std::vector<BboxF> _dynamic_boxs;
        std::vector<BboxF> _dynamic_filter_boxs;

        double _Tlast=0.0;
        struct timeval _start;
        struct timeval _start_thres;
        struct timeval _t_static_start{0,0};

        bool _bfirst = true;
        bool _b_static_first = true;
        bool _static2dynamic = false;
        bool _first_thres = true;
        bool _first_mov = true;
        bool _first_erase = true;
        bool _is_moving = true;
        bool _speaking = false;
        bool _is_missing = false;
        bool _reset_xmin = false;
        bool _first_copy = true;
        //储存上一帧的id与doa信息
        std::vector<int> _doa_id;
        std::vector<float> _doa_covered;

        BboxF _box_out; 
    };

    class  SinglePerson_WindowUpdater: public WindowUpdater
    {
    public:
        SinglePerson_WindowUpdater()
        {
            _cnt = 0;
        }
        enGestureMode process_window_with_gesture(TrackerInputOutput &tracks,std::vector<BboxF> &windowsOut, float& ratio, stGestureInfo gestureinfo) override
        {
            return EN_GESTURE_MODE_NONE;
        }
        virtual void update_move_zoom_state(TrackerInputOutput &tracks, enGestureMode gesturemode, BboxF& showwin, std::vector<BboxF> &windows,stGestureInfo gestureinfo, enZoomMoveType type, float scale_factor=1.1f)
        {
            return;
        }
        bool process_window_by_gesture(TrackerInputOutput &tracks,std::vector<BboxF> &windowsOut, float& ratio, stGestureInfo gestureinfo) override
        {
            return false;
        }
        void process_move_or_zoom(TrackerInputOutput &tracks, std::vector<BboxF> &windowsOut, stGestureInfo gestureinfo, enZoomMoveState state, enZoomMoveType type) override
        {
            return;
        }
        virtual enZoomMoveType judge_zoom_move_type(BboxF & cur_box, BboxF & last_box)
        {
            return EN_NONE_TYPE;
        }

        Status process_window(TrackerInputOutput &tracks, std::vector<BboxF> &windowsOut, float& ratio,int Cmode) override
        {
            std::vector<BboxF> windowsIn = tracks.tracked_bbox();
            int cnt = get_cnt();
            if(cnt < 50)
            {
                windowsOut.push_back(BboxF(0,0, _frameW, _frameH));
                if(!windowsIn.empty())
                {
                    BboxF  w0 = windowsIn[0];
                    if(_window.size() == 0)
                    {
                        _window = w0;
                    }
                    else
                    {
                        const  float decay = 0.9f;
                        _window.xmin(decay * _window.xmin() + (1-decay) * w0.xmin());
                        _window.xmax(decay * _window.xmax() + (1-decay) * w0.xmax());
                        _window.ymin(decay * _window.ymin() + (1-decay) * w0.ymin());
                        _window.ymax(decay * _window.ymax() + (1-decay) * w0.ymax());
                    }
                }
            }
            else
            {
                windowsOut.push_back(_window);
            }
            return CZCV_OK;
        }

    private:
        int get_cnt()
        {
            _cnt +=1;
            if(_cnt > INT_MAX -1)
                _cnt = 0;
            return _cnt;
        }

        int _cnt;
        BboxF  _window;
    };

    void bgr2nv21(const cv::Mat & bgr, cv::Mat& nv21)
    {
        cv::Mat yuv;
        cv::cvtColor(bgr, yuv, cv::COLOR_BGR2YUV_YV12);

        nv21 = cv::Mat::zeros(yuv.rows, yuv.cols, CV_8UC1);
        memcpy(nv21.data, yuv.data, bgr.rows * bgr.cols);
        unsigned char* pnv21v = (unsigned char*)nv21.data + bgr.rows * bgr.cols;
        unsigned char* pnv21u = (unsigned char*)nv21.data + bgr.rows * bgr.cols + 1;
        unsigned char* pyuv_v = (unsigned char*)yuv.data + bgr.rows * bgr.cols;
        unsigned char* pyuv_u = (unsigned char*)yuv.data + bgr.rows * bgr.cols + bgr.rows * bgr.cols / 4;
        for (size_t i = 0; i < bgr.rows * bgr.cols / 4; i++)
        {
            *pnv21u = *pyuv_v;
            *pnv21v = *pyuv_u;

            pyuv_v++;
            pyuv_u++;
            pnv21v += 2;
            pnv21u += 2;
        }
    }

   

    class  PersonCenterStagerImpl : public AsyncFrameWorker
    {
    public:
        cl_context context;
        cl_device_id dev;
        cl_command_queue commandQueue;
        cl_program program;
        cl_kernel kernel;
        cl_mem srcptr;
        cl_mem dstptr;
        bool bopencl_initialesed = false;
        cl_mem global_map1ptr;
        cl_mem global_map2ptr;

        cv::Mat xmap;
        cv::Mat ymap;

        czcv_camera::RgaMat _mblack_strip;

        FILE* file_debug = NULL;

#define HEIGHT ((out_height() + 7) & (~7)) 
#define WIDTH (out_width())

#define SRC_HEIGHT (_final_h) 
#define SRC_WIDTH (_final_w)

        cl_mem createCLMemFromDma(cl_context context, cl_device_id device, cl_mem_flags flags, int dmafd, size_t size)
        {
            cl_mem memoryObject;

            const cl_import_properties_arm props[3] = {
                CL_IMPORT_TYPE_ARM,
                CL_IMPORT_TYPE_DMA_BUF_ARM,
                0,
            };

            cl_int error = CL_SUCCESS;
            memoryObject = opencl_stub::clImportMemoryARMfunc(context, flags, props, &dmafd, size, &error);

            if(error != CL_SUCCESS) {
                LOGE("clImportMemoryARMfunc fail!\n");
                return NULL;
            }

            return memoryObject;
        }

        void opencl_init()
        {
            LOGE("opencl_init %d %d\n", SRC_WIDTH, SRC_HEIGHT);
            cl_platform_id *platform;
            cl_uint num_platform;
            cl_int err;
            err = opencl_stub::clGetPlatformIDs(0, NULL, &num_platform);
            platform = (cl_platform_id*)malloc(sizeof(cl_platform_id)*num_platform);
            err = opencl_stub::clGetPlatformIDs(num_platform, platform, NULL);
            printf("num_platform: %d\n", num_platform);

            cl_int status = 0;
            for(int i=0; i<1; i++)
            {
                size_t size;
                err = opencl_stub::clGetPlatformInfo(platform[i], CL_PLATFORM_NAME, 0, NULL, &size);
                char *PName = (char *)malloc(size);
                err = opencl_stub::clGetPlatformInfo(platform[i], CL_PLATFORM_NAME, size, PName, NULL);
                LOGE("CL_PLATFORM_NAME:%s\n", PName);
                free(PName);

                cl_context_properties cps[3] = {
                    CL_CONTEXT_PLATFORM,
                    (cl_context_properties)platform[i],
                    0};

                cl_context_properties *cprops = cps;
                // 生成 context
                context = opencl_stub::clCreateContextFromType(
                                cprops,
                                CL_DEVICE_TYPE_GPU,
                                NULL,
                                NULL,
                                &status);

                if (status != CL_SUCCESS) {
                    LOGE("Error: Creating Context.(clCreateContexFromType)\n");
                    continue;
                }

                err = opencl_stub::clGetDeviceIDs(platform[i], CL_DEVICE_TYPE_GPU, 1, &dev, NULL);
                size_t valueSize;
                opencl_stub::clGetDeviceInfo(dev, CL_DEVICE_NAME, 0, NULL, &valueSize);
                char* value = (char*) malloc(valueSize);
                opencl_stub::clGetDeviceInfo(dev, CL_DEVICE_NAME, valueSize, value, NULL);
                LOGE("Device Name: %s\n", value);
                free(value);

                commandQueue = opencl_stub::clCreateCommandQueue(context,
                                                    dev,
                                                    0,
                                                    &status);
                if (status != CL_SUCCESS) {
                    LOGE("Error: Create Command Queue. (clCreateCommandQueue)\n");
                    continue;
                }

                FILE* program_handle = fopen((assert_path() + "/remap.cl").c_str(), "r");
                if (program_handle == NULL)
                {
                    LOGE("Error: Open remap.cl error\n");
                    continue;
                }

                fseek(program_handle, 0, SEEK_END);
                size_t program_size = ftell(program_handle);
                rewind(program_handle);
                char* program_buffer = (char*)malloc(program_size + 1);
                fread(program_buffer, sizeof(char), program_size, program_handle);
                program_buffer[program_size] = '\0';
                fclose(program_handle);

                program = opencl_stub::clCreateProgramWithSource(context,
                            1,
                            (const char **)&program_buffer,
                            &program_size,
                            &status);
                if (status != CL_SUCCESS) {
                    LOGE("Error: Loading Binary into cl_program (clCreateProgramWithBinary)\n");
                    continue;
                }
                free(program_buffer);

                std::string buildOptions = "-D INTER_LINEAR -D BORDER_CONSTANT -D T=uchar -D rowsPerWI=1 -D WT=float -D convertToT=convert_uchar_sat_rte -D convertToWT=convert_float -D convertToWT2=convert_float2 -D WT2=float2 -D T=uchar -D T1=uchar -D cn=1 -D ST=uchar -D SRC_DEPTH=0 -cl-fast-relaxed-math";
                status = opencl_stub::clBuildProgram(program, 1, &dev, buildOptions.c_str(), NULL, NULL);
                if (status != CL_SUCCESS) {
                    LOGE("Error: Building Program (clBuildingProgram)\n");
                    size_t retsz = 0;
                    cl_int log_retval = opencl_stub::clGetProgramBuildInfo(program, dev,
                                                        CL_PROGRAM_BUILD_LOG, 0, 0, &retsz);
                    char* buffer = (char*)malloc(retsz + 16);
                    log_retval = opencl_stub::clGetProgramBuildInfo(program, dev,
                                                    CL_PROGRAM_BUILD_LOG, retsz+1, buffer, &retsz);

                    buffer[retsz] = 0;
                    LOGE("%s\n", buffer);
                    free(buffer);
                    continue;
                }
#if 1
                kernel = opencl_stub::clCreateKernel(program, "remap_2_32FC1_yuv", &status);
                //kernel = clCreateKernel(program, "remap_indentity", &status);

                if (status != CL_SUCCESS) {
                    LOGE("Error: Creating Kernel from program.(clCreateKernel)\n");
                    continue;
                }       
#endif
                int src_step = SRC_WIDTH;
                int src_offset = 0;
                int src_rows = SRC_HEIGHT;
                int src_cols = SRC_WIDTH;
                int dst_step = WIDTH;
                int dst_offset = 0;
                int dst_rows = ((HEIGHT + 7) & (~7));
                int dst_cols = ((WIDTH + 15) & (~15));

#define MAP_WIDTH (WIDTH)
#define MAP_HEIGHT (HEIGHT)

                int map1_step = dst_cols * sizeof(float);
                int map1_offset = 0;
                int map2_step = dst_cols * sizeof(float);
                int map2_offset = 0;

                unsigned char scalar = 0;

                status = opencl_stub::clSetKernelArg(kernel, 1, sizeof(int), (void *)&src_step);
                status = opencl_stub::clSetKernelArg(kernel, 2, sizeof(int), (void *)&src_offset);
                status = opencl_stub::clSetKernelArg(kernel, 3, sizeof(int), (void *)&src_rows);
                status = opencl_stub::clSetKernelArg(kernel, 4, sizeof(int), (void *)&src_cols);

                status = opencl_stub::clSetKernelArg(kernel, 6, sizeof(int), (void *)&dst_step);
                status = opencl_stub::clSetKernelArg(kernel, 7, sizeof(int), (void *)&dst_offset);
                status = opencl_stub::clSetKernelArg(kernel, 8, sizeof(int), (void *)&dst_rows);
                status = opencl_stub::clSetKernelArg(kernel, 9, sizeof(int), (void *)&dst_cols);
#if 1
                status = opencl_stub::clSetKernelArg(kernel, 11, sizeof(int), (void *)&map1_step);
                status = opencl_stub::clSetKernelArg(kernel, 12, sizeof(int), (void *)&map1_offset);
                status = opencl_stub::clSetKernelArg(kernel, 14, sizeof(int), (void *)&map2_step);
                status = opencl_stub::clSetKernelArg(kernel, 15, sizeof(int), (void *)&map2_offset);

                status = opencl_stub::clSetKernelArg(kernel, 16, sizeof(unsigned char), (void *)&scalar);
#endif

                FILE* fxmap = fopen((assert_path() + "/xmap.bin").c_str(), "rb");
                if (fxmap == NULL)
                {
                    LOGE("Error: open xmap\n");
                    continue;
                }

                FILE* fymap = fopen((assert_path() + "/ymap.bin").c_str(), "rb");
                if (fymap == NULL)
                {
                    LOGE("Error: open ymap\n");
                    continue;
                }


                xmap = cv::Mat(MAP_HEIGHT, MAP_WIDTH, CV_32FC1);
                ymap = cv::Mat(MAP_HEIGHT, MAP_WIDTH, CV_32FC1);

#define GLOBAL_MAP_WIDTH (1920)
#define GLOBAL_MAP_HEIGHT (1080)
                float* xmap_data = (float*)malloc(GLOBAL_MAP_WIDTH * GLOBAL_MAP_HEIGHT*sizeof(float));
                float* ymap_data = (float*)malloc(GLOBAL_MAP_WIDTH * GLOBAL_MAP_HEIGHT*sizeof(float));
                fread(xmap_data, GLOBAL_MAP_WIDTH * GLOBAL_MAP_HEIGHT, sizeof(float), fxmap);
                fread(ymap_data, GLOBAL_MAP_WIDTH * GLOBAL_MAP_HEIGHT, sizeof(float), fymap);

                fclose(fxmap);
                fclose(fymap);

                cv::Mat temp_xmap(GLOBAL_MAP_HEIGHT, GLOBAL_MAP_WIDTH, CV_32FC1, xmap_data);
                cv::Mat temp_ymap(GLOBAL_MAP_HEIGHT, GLOBAL_MAP_WIDTH, CV_32FC1, ymap_data);
                if (GLOBAL_MAP_HEIGHT != MAP_HEIGHT || GLOBAL_MAP_WIDTH != MAP_WIDTH)
                {
                    cv::resize(temp_xmap, xmap, cv::Size(MAP_WIDTH, MAP_HEIGHT), cv::INTER_LINEAR);
                    cv::resize(temp_ymap, ymap, cv::Size(MAP_WIDTH, MAP_HEIGHT), cv::INTER_LINEAR);
                }
                else
                {
                    xmap = temp_xmap.clone();
                    ymap = temp_ymap.clone();
                }

                global_map1ptr = opencl_stub::clCreateBuffer(
                                    context,
                                    CL_MEM_ALLOC_HOST_PTR | CL_MEM_READ_WRITE,
                                    MAP_WIDTH * MAP_HEIGHT * sizeof(float),
                                    NULL,
                                    &status);
                if (status != CL_SUCCESS) {
                    LOGE("Error: CclCreateBuffer global_map1ptr.\n");
                    continue;
                }

                global_map2ptr = opencl_stub::clCreateBuffer(
                                    context,
                                    CL_MEM_ALLOC_HOST_PTR | CL_MEM_READ_WRITE,
                                    MAP_WIDTH * MAP_HEIGHT * sizeof(float),
                                    NULL,
                                    &status);
                if (status != CL_SUCCESS) {
                    LOGE("Error: CclCreateBuffer global_map2ptr.\n");
                    continue;
                }

                LOGE("opencl init global_map: %p %p\n", global_map1ptr, global_map2ptr);
                status = opencl_stub::clEnqueueWriteBuffer(commandQueue,
                            global_map1ptr, CL_TRUE, 0,
                            HEIGHT * WIDTH * sizeof(float), (void*)xmap.data, 0, NULL, NULL);
                if (status != CL_SUCCESS) {
                    printf("Error: Write buffer global_map1ptr queue\n");
                    continue;
                }

                status = opencl_stub::clEnqueueWriteBuffer(commandQueue,
                            global_map2ptr, CL_TRUE, 0,
                            HEIGHT * WIDTH * sizeof(float), (void*)ymap.data, 0, NULL, NULL);
                if (status != CL_SUCCESS) {
                    printf("Error: Write buffer global_map2ptr queue\n");
                    continue;
                }

                free(xmap_data);
                free(ymap_data);

                LOGE("opencl init succeed\n");
            }

            bopencl_initialesed = true;
        }

        void release_opencl()
        {
#ifndef _WIN32
            LOGE("release_opencl\n");
            if (!bopencl_initialesed)
                return;
            opencl_stub::clReleaseKernel(kernel);

            opencl_stub::clReleaseProgram(program);
            opencl_stub::clReleaseCommandQueue(commandQueue);
            opencl_stub::clReleaseContext(context);
            opencl_stub::clReleaseMemObject(global_map1ptr);
            opencl_stub::clReleaseMemObject(global_map2ptr);

            bopencl_initialesed = false;

            LOGE("release_opencl finish\n");
#endif
        }

        PersonCenterStagerImpl(int preview_width, int preview_height, LOG_LEVEL logLevel = CZCV_LOG_INFO):
        windowUpdater(true), windowUpdaterSub(false)
        {
            _outframeReady = false;
            _previeww = preview_width;
            _previewh = preview_height;
            _final_w = ((preview_width+15)&(~15));
            _final_h = ((preview_height+7)&(~7));
            _doaRecordArray.resize(_doaRecordNum);
#ifdef __ANDROID__           
            _librgaopsLibHandle = dlopen("librgaCopyScale.so", RTLD_NOW); 
            if (_librgaopsLibHandle == NULL) 
            {
                LOGE("open librgaCopyScale.so fail: %sn", dlerror());

                _dataptr = new unsigned char [_final_w * _final_h * 3 / 2];
                _outframe = cv::Mat(_final_h * 3 / 2, _final_w, CV_8UC1, _dataptr);
            }
            else 
            {
                _rgaops = std::make_shared<rga_interface_t>();
                //_rgaops->init = (rgaInitFun)dlsym(_librgaopsLibHandle, "init");
                //_rgaops->scale = (rgaScaleFun)dlsym(_librgaopsLibHandle, "scale"); 
                //_rgaops->copy = (rgaCopyFun)dlsym(_librgaopsLibHandle, "copy"); 
                _rgaops->cropScale = (rgaCropScaleFun)dlsym(_librgaopsLibHandle, "crop_scale"); 
                _rgaops->cropScaleRgb = (rgaCropScaleRgbFun)dlsym(_librgaopsLibHandle, "crop_scale_rgb"); 
                _rgaops->malloc_rga = (rgaMallocFun)dlsym(_librgaopsLibHandle, "rga_malloc"); 
                _rgaops->yuv2rgb = (rgaNv12torgbFun)dlsym(_librgaopsLibHandle, "nv12torgb"); 
                _rgaops->mat_fill = (rgaMatfillFun)dlsym(_librgaopsLibHandle, "color_fill_rgb"); 
                _rgaops->free = (rgaFreeFun)dlsym(_librgaopsLibHandle, "rga_free"); 
                _rgaops->wrapbuffer_fd = (rgaWrapbufferFdFun)dlsym(_librgaopsLibHandle, "rga_wrapbuffer_fd"); 
                _rgaops->cropScaleRoi = (rgaCropScaleRoiFun)dlsym(_librgaopsLibHandle, "crop_scale_roi");
                
                //_rgaops->merge = (rgaMergeFun)dlsym(_librgaopsLibHandle, "merge"); 
                if (NULL == _rgaops->cropScale)
                {
                    LOGE("get function fail");
                }
                if (NULL == _rgaops->cropScaleRgb)
                {
                    LOGE("get function fail");
                }
                if (NULL == _rgaops->yuv2rgb || NULL == _rgaops->mat_fill)
                {
                    LOGE("get function fail");
                }
                if (NULL == _rgaops->malloc_rga)
                {
                    LOGE("get function malloc_rga fail");
                }
                if (NULL == _rgaops->free)
                {
                    LOGE("get function fail");
                }

                if (NULL != _rgaops->init)
                {
                    int ret = _rgaops->init(_final_w, _final_h, &_tempVirAddr);
                    if (ret != 0)
                    {
                        LOGE("_rgaops init fail: %d", ret);
                    }
                }

                windowUpdater._rgaops = _rgaops;
            }

            _last_win =  BboxF(0, 0, 0, 0);
            _last_move_zoom_win = BboxF(0, 0, 0, 0);
            _last_win_sub =  BboxF(0, 0, 0, 0);
            _last_move_zoom_win_sub = BboxF(0, 0, 0, 0);

            windowUpdater.frame_w(_previeww);
            windowUpdater.frame_h(_previewh);
            windowUpdater._down_scale = _down_scale;

            _mblack_strip.handle = _rgaops->malloc_rga(&_mblack_strip.viraddr,&_mblack_strip.phyaddr,_previeww / 2, 16, czcv_pixel_format_t::HAL_PIXEL_FORMAT_RGB_888);
            memset(_mblack_strip.viraddr, 0, _previeww * 16);
            memset((unsigned char*)(_mblack_strip.viraddr) + _previeww * 16, 128, _previeww * 16 / 2);
            _rga_mat.handle = _rgaops->malloc_rga(&_rga_mat.viraddr,&_rga_mat.phyaddr,_final_w / _down_scale, _final_h / _down_scale, czcv_pixel_format_t::HAL_PIXEL_FORMAT_RGB_888); 
#else
            _dataptr = new unsigned char[preview_height * 3 * preview_width / 2];
            _outframe = cv::Mat(preview_height * 3 / 2, preview_width, CV_8UC1, _dataptr);
#endif

                  
            // file_debug = fopen("/data/vendor/camera/czcv_debug.txt","w+");
            // if (NULL == file_debug)
            // {
            //     LOGE("PersonCenterStagerImpl create debug file /data/vendor/camera/czcv_debug.txt failed!");
            // }
        }
        ~PersonCenterStagerImpl()
        {         
            release();
        }

        void release()
        {
            stop();
            windowUpdater.stop();

            release_opencl();

            if (_librgaopsLibHandle)
            {                      
                if (_mblack_strip.handle != NULL)
                {
                    int ret = _rgaops->free(_mblack_strip.handle);
                    _mblack_strip.handle = nullptr;
                }   

                if (_rga_mat.handle != NULL)
                {
                    int ret = _rgaops->free(_rga_mat.handle);
                    _rga_mat.handle = nullptr;    
                    _rga_mat.phyaddr = -1; 
                }                    
            }  

             _outframe.release();  
            if (_dataptr != nullptr)
            {
                delete _dataptr;
                _dataptr = nullptr;
            }

            if (file_debug != NULL)
            {
                fclose(file_debug);
                file_debug = NULL;
            }
#ifdef __ANDROID__
            if (_librgaopsLibHandle)
            {
                dlclose(_librgaopsLibHandle);
		        _librgaopsLibHandle = NULL;
            }
#endif
        }

        Status stop() override
        {
            set_event(STOP_LOOP);
            while (is_running() || doa_is_running() || is_running(false) || ges_is_running())
            {
                std::this_thread::sleep_for(std::chrono::milliseconds(5));
            }
            return  CZCV_OK;
        }

        void stop_async()
        {
            threadid(threadid() + 1);
        }

        void set_use4k(int use_4k)
        {
            _use_4k = use_4k;
        }

        Status set_detector(DetectorID detectorId)
        {  
            _detectorId = detectorId;
            return CZCV_OK;
        }

        Status set_tracker(TrackerID trackerId)
        {
            _trackerId = trackerId;
            return CZCV_OK;
        }

        Status on_set_arg(DynamicParams& params)
        {
            _gparams = params;
            return  CZCV_OK;
        }

        Status bind_viewer(std::shared_ptr<Base_PersonViewer> &personViewerPtr)
        {
            _personViewerPtr = personViewerPtr;
            _personViewerPtr->bind_ops_interface(_rgaops);
            return  CZCV_OK;
       }

        /**
         * @brief init algorithm models
         * @param modelDir
         * @return @Status
         */
        Status init_models_async(std::vector<std::string>& detModelCfg, int only_cpu)
        {
            _only_cpu = only_cpu;
            _modelPaths = detModelCfg;
            windowUpdater._modelPaths.push_back(_modelPaths[6]);
            return CZCV_OK;
        }

        Status init_model_assert(std::vector<std::string>& assertModelCfg, int only_cpu)
        {
            
            _only_cpu = only_cpu;
            _assertmodelPaths = assertModelCfg;
            return CZCV_OK;
        }

        Status init_model_hand(std::vector<std::string>& assertModelCfg, int only_cpu)
        {         
            _only_cpu = only_cpu;
            _handModelPaths = assertModelCfg;
            return CZCV_OK;
        }
        /************************************************************/

        /**
         *
         * @return  @Status
         */
        Status start()
        {
#ifndef _WIN32
            //opencl_init();   
            _t = std::thread(&PersonCenterStagerImpl::thread_loop, this, threadid(), true);
            _t.detach();

            char device_model[PROPERTY_VALUE_MAX] = {0};
            property_get("persist.vendor.czur.hardware.model", device_model, "");
            
            bool device_studio = strcasecmp(device_model, "studio") == 0;
            if (false == device_studio)
            {
                _t1 = std::thread(&PersonCenterStagerImpl::thread_loop, this, threadid(), false);
                _t1.detach();
            }
           
            _t2 = std::thread(&PersonCenterStagerImpl::thread_loop_gesture, this, threadid());
            _t2.detach();
            
            _tDoa = std::thread(&PersonCenterStagerImpl::thread_doa_loop, this, threadid());
            _tDoa.detach();
#endif
            return CZCV_OK;
        }

        /**
         * @brief  push frame to
         * @param img_blob  see @ImageBlob
         * @return  @Status
         */
        Status push_image_blob(ImageBlob& imgBlob)
        {
            //TODO no copy and no convert here
            //TODO support sync function

            // this thread do data process and viewer
            push_frame(imgBlob.frameCopiedBGR);

            return CZCV_OK;
        }

        /************************************************************/

        bool out_frame_ready()
        {
            std::unique_lock<std::mutex> ulk(_muoutFrame);
            return  _outframeReady;
        }

        void get_out_frame(cv::Mat &ref, bool withCopy = false)
        {
            std::unique_lock<std::mutex> ulk(_muoutFrame);
            if(withCopy)
            {
                ref = _outframe.clone();
            }
            else
            {
                ref = _outframe;
            }
        }
        void  set_out_frame(cv::Mat &frame)
        {
            std::unique_lock<std::mutex> ulk(_muoutFrame);
            _outframe = frame.clone();
            _outframeReady = true;
        }

        void  set_frame(cv::Mat &frame) override
        {
            std::unique_lock<std::mutex> ulk(_muFrame);
            if (dst_vir_addr() != nullptr)
            {
                _frame = frame;
                _frameReady = true;
                return; 
            }

            if (NULL == _rgaops)
            {
                if (_outframe.cols != frame.cols || _outframe.rows != frame.rows)
                {             
                    if (_dataptr)
                    {
                        delete _dataptr;                       
                    }  

                    _dataptr = new unsigned char[frame.cols * frame.rows];
                    _outframe = cv::Mat(frame.rows, frame.cols, CV_8UC1, _dataptr);
                }

                frame.copyTo(_outframe);
            }
            else
            {
                int ret = _rgaops->copy(phy_addr());
                if(ret != 0)
                {
                    LOGE("rga copy failed: %d\n", ret);
                }
            }
                             
            _frame = _outframe;
            _frameReady = true;
        }

        int run_time = 0;
        Status opencl_run()
        {
            Status ret = CZCV_OK;
#ifndef _WIN32
            //double t1 = cv::getTickCount();
            if (!bopencl_initialesed)
            {
                return CZCV_INIT_MODEL_ERR;
            }

#define pi (3.141592653f)
            
            int dst_rows = ((HEIGHT + 7) & (~7));
            int dst_cols = ((WIDTH + 7) & (~7));

            size_t globalThreads[] = {(size_t)dst_cols / 4, (size_t)dst_rows};

            srcptr = createCLMemFromDma(context,
                                        dev,
                                        CL_MEM_READ_ONLY,
                                        phy_addr(),
                                        SRC_WIDTH * SRC_HEIGHT * 3 / 2);

            dstptr = createCLMemFromDma(context,
                                        dev,
                                        CL_MEM_WRITE_ONLY,
                                        dst_phy_addr(),
                                        dst_rows * dst_cols * 3 / 2);

            cl_int status;


#if 1
            status = opencl_stub::clSetKernelArg(kernel, 0, sizeof(cl_mem), (void *)&srcptr);
            if (status != CL_SUCCESS) {
                LOGE("Error: Setting kernel 0 argument. (clSetKernelArg)\n");
                return CZCV_RUN_KERNEL_ERR;
            }

            status = opencl_stub::clSetKernelArg(kernel, 5, sizeof(cl_mem), (void *)&dstptr);
            if (status != CL_SUCCESS) {
                LOGE("Error: Setting kernel 5 argument. (clSetKernelArg)\n");
                return CZCV_RUN_KERNEL_ERR;
            }

            status = opencl_stub::clSetKernelArg(kernel, 10, sizeof(cl_mem), (void *)&global_map1ptr);
            if (status != CL_SUCCESS) {
                LOGE("Error: Setting kernel 10 argument. (clSetKernelArg)\n");
                return CZCV_RUN_KERNEL_ERR;
            }

            status = opencl_stub::clSetKernelArg(kernel, 13, sizeof(cl_mem), (void *)&global_map2ptr);
            if (status != CL_SUCCESS) {
                LOGE("Error: Setting kernel 13 argument. (clSetKernelArg)\n");
                return CZCV_RUN_KERNEL_ERR;
            }
#endif

            globalThreads[0] = (size_t)dst_cols;

            status = opencl_stub::clEnqueueNDRangeKernel(commandQueue, kernel,
                                    2, NULL, globalThreads,
                                    NULL, 0,
                                    NULL, NULL);
            if (status != CL_SUCCESS) {
                LOGE("Error: Enqueueing kernel %d\n", status);
                return CZCV_RUN_KERNEL_ERR;
            }

            // status = opencl_stub::clEnqueueReadBuffer(commandQueue, dstptr, CL_TRUE, 0, dst_rows * dst_cols * 3 / 2, dst_vir_addr(), 0, NULL, NULL);
            // if (status != CL_SUCCESS) {
            //     LOGE("Error: clEnqueueReadBuffer dstptr: %d\n", status);
            //     return CZCV_RUN_KERNEL_ERR;
            // }

            status = opencl_stub::clFinish(commandQueue);
            if (status != CL_SUCCESS) {
                LOGE("Error: Finish command queue\n");
                return CZCV_RUN_KERNEL_ERR;
            }

            opencl_stub::clReleaseMemObject(srcptr);
            opencl_stub::clReleaseMemObject(dstptr);
            // double t2 = cv::getTickCount();

            // if(run_time % 20 == 0)
            // {
            //     LOGE("opencl_run duration0 is %.4f\n",
            //         (t2 - t1) * 1000 / cv::getTickFrequency());
            // }
            // run_time++;
#endif
            return ret;
        }

        int g_frame_id = -1;
        int last_phy_addr = -1;
        void* last_vir_addr = nullptr;
        Status run(ImageBlob &imgBlob,int Cmode,int low_consumption)
        {                 
            Status ret;   
      
            //double t1 = cv::getTickCount();  
            set_consumption(low_consumption);
            phy_addr(imgBlob.phy_addr); 
            ret = push_frame(imgBlob.frameCopiedBGR); 

            frame_worker(imgBlob.frameCopiedBGR, Cmode);

            //double t2 = cv::getTickCount();

            //LOGE("[czcv_camera]run time: %f, phy_addr: %d\n", (t2 - t1) * 1000/ cv::getTickFrequency(), imgBlob.phy_addr);
            
            if (g_frame_id % 15 == 0)
            {
                std::string fpath = "/mnt/imgs/" + std::to_string(g_frame_id) + ".jpg";
                // FILE* f = fopen(fpath.c_str(), "wb");
                // if (f != NULL)
                // {
                //     fwrite(imgBlob.frameCopiedBGR.data, 1, imgBlob.frameCopiedBGR.cols*imgBlob.frameCopiedBGR.rows,f);
                //     fclose(f);
                // }

                cv::Mat bgr;
                cv::cvtColor(imgBlob.frameCopiedBGR, bgr, cv::COLOR_YUV2BGR_NV12);
                cv::imwrite(fpath, bgr);             
            // }
            // g_frame_id++;
            // if (g_frame_id % 10 == 0)
            // {
            //     std::string fpath = "/mnt/imgs/" + std::to_string(g_frame_id) + ".bin";
            //     FILE* f = fopen(fpath.c_str(), "wb");
            //     if (f != NULL)
            //     {
            //         fwrite(imgBlob.frameCopiedBGR.data, 1, imgBlob.frameCopiedBGR.cols*imgBlob.frameCopiedBGR.rows,f);
            //         fclose(f);
            //     }
            }
            //g_frame_id++;
            
            return ret;                 
        }

        //int g_frame_id_sub = 0;
        Status run_sub(ImageBlob &imgBlob)
        {                 
            Status ret = CZCV_OK;   
            //ret = _rgaops->cropScale(imgBlob.phy_addr, sub_buf_mat.phyaddr, imgBlob.frameCopiedBGR.cols, imgBlob.frameCopiedBGR.rows * 2 / 3, 0, 0, imgBlob.frameCopiedBGR.cols, imgBlob.frameCopiedBGR.rows * 2 / 3, imgBlob.frameCopiedBGR.cols, imgBlob.frameCopiedBGR.rows * 2 / 3);
            //double t1 = cv::getTickCount(); 
            phy_addr_sub(imgBlob.phy_addr); 
            set_frame_ready(true, false);
            //double t2 = cv::getTickCount();
            
            // if (g_frame_id_sub % 10 == 0)
            // {
            //     std::string fpath = "/mnt/imgs/" + std::to_string(g_frame_id_sub) + "_l.bin";
            //     FILE* f = fopen(fpath.c_str(), "wb");
            //     if (f != NULL)
            //     {
            //         fwrite(imgBlob.frameCopiedBGR.data, 1, imgBlob.frameCopiedBGR.cols*imgBlob.frameCopiedBGR.rows,f);
            //         fclose(f);
            //     }
            // }
            // g_frame_id_sub++;

            std::vector<BboxF> windows;
            int inwidth = imgBlob.frameCopiedBGR.cols;
            int inheight = imgBlob.frameCopiedBGR.rows;
            if (imgBlob.frameCopiedBGR.channels() == 1)
            {
                inheight = imgBlob.frameCopiedBGR.rows * 2 / 3;
            }
      
            windowUpdater.phy_addr_sub(imgBlob.phy_addr);
            //windowUpdaterSub.doa(doa);
            windowUpdaterSub.frame_w(_previeww);
            windowUpdaterSub.frame_h(_previewh);
            windowUpdaterSub.input_w(inwidth);
            windowUpdaterSub.input_h(inheight);
            windowUpdaterSub.set_use4k(_use_4k);

            if (out_width() > 0 && out_height() > 0)
            {
                windowUpdaterSub.buffer_w(out_width());
                windowUpdaterSub.buffer_h(out_height());
            }
            else
            {
                windowUpdaterSub.buffer_w(inwidth);
                windowUpdaterSub.buffer_h(inheight);
            }
                    
            return ret;                 
        }

        int phy_addr()
        {
            std::unique_lock<std::mutex> ulk(_muPhyAddr);        
            return _phyaddr;
        }

        void phy_addr(int phyaddr)
        {
            std::unique_lock<std::mutex> ulk(_muPhyAddr);  
            _phyaddr = phyaddr;
        }

        int phy_addr_sub()
        {
            std::unique_lock<std::mutex> ulk(_muPhyAddrSub);        
            return _phyaddr_sub;
        }

        void phy_addr_sub(int phyaddr)
        {
            std::unique_lock<std::mutex> ulk(_muPhyAddrSub);  
            _phyaddr_sub = phyaddr;
        }

        void frame_worker_4k()
        {
            std::vector<BboxF> windows;
            // if (mode() == enTrackModeNo)
            // {
            //     //LOGE("wrong mode\n");
            //     return;
            // }
            TrackerInputOutput tracks;
            fetch_tracks(tracks);

            float realdoa = -1;
            float doa = -1;
            if (mode() == enTrackModeVideoandAudio)
            {
                doa = read_doa(&realdoa);   

                doa = read_doa(detect_time);  
            }
            windowUpdater.doa(doa);
            windowUpdater.frame_w(_previeww);
            windowUpdater.frame_h(_previewh);
            windowUpdater.input_w(3840);
            windowUpdater.input_h(2160);
            windowUpdater.set_use4k(_use_4k);

            if (out_width() > 0 && out_height() > 0)
            {
                windowUpdater.buffer_w(out_width());
                windowUpdater.buffer_h(out_height());
            }
            else
            {
                windowUpdater.buffer_w(3840);
                windowUpdater.buffer_h(2160);
            }

            float ratio = 1.0f; 
            windowUpdater.process_window(tracks, windows, ratio, 1);
            _call_back_box = windows[0];
        }

        bool is_valid_gesture(int gestureid)
        {
            return gestureid == CZCV_GESTURE_PALM || gestureid == CZCV_GESTURE_STOP 
                || gestureid == CZCV_GESTURE_PEACE || gestureid == CZCV_GESTURE_PEACE_INV || gestureid == CZCV_GESTURE_THUMB_INDEX;          
        }

        bool check_same_gesture(stGestureInfo info1, stGestureInfo info2)
        {
            float same_gesture_thresh = 0.5f;
            if (info1.box.iou_with(info2.box) < same_gesture_thresh && info1.gesture_id != CZCV_GESTURE_THUMB_INDEX)
            {
                return false;
            }

            if (info1.gesture_id == CZCV_GESTURE_PALM || info1.gesture_id == CZCV_GESTURE_STOP)
            {
                if (info1.instace_id == info2.instace_id && (CZCV_GESTURE_PALM == info2.gesture_id || CZCV_GESTURE_STOP == info2.gesture_id))
                {
                    return true;
                }
            }
            else if (info1.gesture_id == CZCV_GESTURE_PEACE || info1.gesture_id == CZCV_GESTURE_STOP)
            {
                if (info2.gesture_id == CZCV_GESTURE_PEACE || info2.gesture_id == CZCV_GESTURE_STOP)
                {
                    return true;
                }
            }
            else if (info1.gesture_id == CZCV_GESTURE_THUMB_INDEX)
            {
                if (info1.instace_id == info2.instace_id && info1.gesture_id == info2.gesture_id)
                {
                    return true;
                }
            }

            return false;
        }

        bool gesture_process(std::vector<stGestureRecResult>& gestureresults, stGestureInfo& gestureinfo, bool bsub=false)
        {          
            std::vector<stGestureInfo> & gesture_info = bsub ? _gesture_info_sub : _gesture_info;
            WindowUpdater & windowUpdaterlocal = bsub ? windowUpdaterSub : windowUpdater;
            stGestureInfo & last_gesture_info = bsub ? _last_gesture_info_sub : _last_gesture_info;

            int count_thresh = 15;
            int miss_count_thresh = 8;
            std::vector<bool> flags( gesture_info.size(), false);

            std::vector<stGestureInfo> newinfo;
            for (auto& result: gestureresults)
            {
                if (false == is_valid_gesture(result.clsid))
                {
                    continue;
                }

                std::vector<stGestureInfo>::iterator it = gesture_info.begin();
                bool find = false;
                for(; it != gesture_info.end(); ++it)
                {
                    stGestureInfo info;
                    info.instace_id = result.det_instance_id;
                    if (windowUpdaterlocal._det_id_map.count(result.det_instance_id) > 0)
                    {
                        info.instace_id = windowUpdaterlocal._det_id_map[result.det_instance_id];
                    }
                    info.gesture_id = result.clsid;
                    info.box = BboxF(result.rectf.x0, result.rectf.y0, result.rectf.x1, result.rectf.y1);
                    if (check_same_gesture(*it, info))
                    {
                        it->count++;
                        it->miss_count = 0;
                        it->box = info.box;
                        find = true;
                        flags[it - gesture_info.begin()] = true;
                        break;
                    }                
                }

                //在已保存的手势信息里没有查找到相同的手势，认为是新的手势
                if (!find)
                {
                    //if (result.clsid != 20 && result.clsid != 24 && result.clsid != 21 && result.clsid != 22 && result.clsid != 31)
                    if (false == is_valid_gesture(result.clsid))
                    {
                        continue;
                    }

                    stGestureInfo info;
                    info.instace_id = result.det_instance_id;
                    if (windowUpdaterlocal._det_id_map.count(result.det_instance_id) > 0)
                    {
                        info.instace_id = windowUpdaterlocal._det_id_map[result.det_instance_id];
                    }
                    info.gesture_id = result.clsid;
                    info.count = 1;
                    info.bnew = true;
                    info.miss_count = 0;
                    info.box = BboxF(result.rectf.x0, result.rectf.y0, result.rectf.x1, result.rectf.y1);
                    newinfo.push_back(info);
                }               
            }

            for (size_t n = 0;n < flags.size(); ++n)
            {
                //已保存的手势信息里新的一帧没有检测到，miss_count加1
                if (flags[n] == false)
                {
                    gesture_info[n].count = 0;
                    gesture_info[n].miss_count++;
                }
            }

            for (auto & info: gesture_info)
            {
                //已保存的手势信息里miss_count超过门限则删除
                if (info.miss_count > miss_count_thresh) // || (info.miss_count > 1 && (info.gesture_id == 31 || info.gesture_id == 19) ))
                {
                    continue;
                }
                newinfo.push_back(info);
                //LOGE("newinfo: %d", info.gesture_id);
            }

            gesture_info = newinfo;

            gestureinfo.instace_id = -1;
            gestureinfo.gesture_id = -1;
            gestureinfo.bnew = false;

            for (auto& info : gesture_info)
            {
                //如果上一次上报的手势还在，本次优先上报同样的手势
                if (check_same_gesture(info, last_gesture_info))
                {
                    if (info.count > count_thresh ) // || (info.count == 1 && (info.gesture_id == 31 || info.gesture_id == 19)))
                    {
                        gestureinfo = info;
                        gestureinfo.bnew = false;
                        last_gesture_info = gestureinfo;
                        return true;
                    } 
                }
            }

            last_gesture_info = gestureinfo;
            
            for (auto& info : gesture_info)
            {
                //上报检测次数达到阈值的手势
                if (info.count > count_thresh ) // || (info.count == 1 && (info.gesture_id == 31 || info.gesture_id == 19)))
                {
                    gestureinfo = info;
                    info.bnew = false;
                    last_gesture_info = info;                 
                    //LOGE("gestureinfo: %d %d", info.gesture_id, gestureinfo.bnew);
                    return true;
                }
            }

            return false;                 
        }
        

        int g_index = 0;
        int simulated_id = 0;
        int simulated_x0 = 1920;
        int simulated_y0 = 100;
        int simulated_x1 = 2520;
        int simulated_y1 = 1800;
        int gesture_x0 = 1920 + 100;
        int gesture_y0 = 100+ 200;
        int gesture_x1 = 1920 + 300;
        int gesture_y1 = 100 + 400;
        bool in_simulated()
        {
            return  (simulated_id > 200 && (simulated_id % 200 < 15 || (simulated_id % 200 > 100 && simulated_id % 200 < 110)));
        }
        
        void frame_worker(cv::Mat &frame,int Cmode) override
        {
            std::vector<BboxF> windows;
            int inwidth = frame.cols;
            int inheight = frame.rows;
            if (frame.channels() == 1)
            {
                inheight = frame.rows * 2 / 3;
            }

            // if (mode() == enTrackModeNo)
            // {
            //     //LOGE("wrong mode\n");
            //     return;
            // }
        
            // DetInputOutput dets;
            // fetch_dets(dets);

            TrackerInputOutput tracks;
            fetch_tracks_with_gesture(tracks);
     
            //simulated_id++;
            //LOGE("simulated: %d %d %d", simulated_id, tracks.tracked_bbox().size(), tracks.gestureResults().size());

            std::vector<BboxF> in_bbox = tracks.in_bbox();

            for (auto & box: in_bbox)
            {
                box.xmin(box.xmin() * _down_scale);
                box.xmax(box.xmax() * _down_scale);
                box.ymin(box.ymin() * _down_scale);
                box.ymax(box.ymax() * _down_scale);
            }  
            
            tracks.in_bbox(in_bbox);

            std::vector<BboxF> tracked_bbox = tracks.tracked_bbox();

            for (auto & box: tracked_bbox)
            {
                box.xmin(box.xmin() * _down_scale);
                box.xmax(box.xmax() * _down_scale);
                box.ymin(box.ymin() * _down_scale);
                box.ymax(box.ymax() * _down_scale);
            }  
            
            tracks.tracked_bbox(tracked_bbox);

            std::vector<stGestureRecResult>& gestureResults = tracks.gestureResults();
            for (auto & result: gestureResults)
            {
                result.rectf.x0 *= _down_scale;
                result.rectf.x1 *= _down_scale;
                result.rectf.y0 *= _down_scale;
                result.rectf.y1 *= _down_scale;
            }   

            //fetch_tracks(tracks);
            //std::vector<BboxF> windowsRaw = dets.bbox();
                                                                                                                                                           
            //float realdoa = -1;
            float doa = -1;
            if (mode() == enTrackModeVideoandAudio)
            {
                //doa = read_doa(&realdoa);   
                doa = read_doa(detect_time);  
            }

            if (file_debug != NULL)
            {
                fprintf(file_debug, "in_bbox:");
                for(auto & box: tracks.in_bbox())
                {
                    fprintf(file_debug, " %f %f %f %f %d %d", box.xmin(), box.ymin(), box.xmax(), box.ymax(), box.class_id(), box.instance_id());
                }
                fprintf(file_debug, "; tracked_bbox:");
                for(auto & box: tracks.tracked_bbox())
                {
                    fprintf(file_debug, " %f %f %f %f %d %d", box.xmin(), box.ymin(), box.xmax(), box.ymax(), box.class_id(), box.instance_id());
                }
                fprintf(file_debug, "; gesture:");
                for (auto & result: gestureResults)
                {
                    fprintf(file_debug, " %f %f %f %f %d %d", result.rectf.x0, result.rectf.y0, result.rectf.x1, result.rectf.y1, result.clsid, result.det_instance_id);
                }
                fprintf(file_debug, "; doa: %f;\n", doa);
            }

            windowUpdater.doa(doa);
            windowUpdater.frame_w(_previeww);
            windowUpdater.frame_h(_previewh);
            windowUpdater.input_w(inwidth);
            windowUpdater.input_h(inheight);
            windowUpdater.set_use4k(_use_4k);

            if (out_width() > 0 && out_height() > 0)
            {
                windowUpdater.buffer_w(out_width());
                windowUpdater.buffer_h(out_height());
            }
            else
            {
                windowUpdater.buffer_w(inwidth);
                windowUpdater.buffer_h(inheight);
            }           

            windowUpdater.phy_addr(phy_addr());
            windowUpdater._bViewChange = false;
                     
            float ratio = 1.0f;
            float aspcet_ratio = 1.0f;
            if (mode() != enTrackModeNo)
            {
                windowUpdater.process_window(tracks, windows, ratio, Cmode);
            }
            else
            {
                if (gesture_mode())
                {
                    windowUpdater.process_window(tracks, windows, ratio, Cmode);
                }
                windowUpdater._longView = false;
                windows.clear();
                windows.push_back(BboxF(0.0f, 0.0f, windowUpdater.frame_w() - 1, windowUpdater.frame_h() - 1));
            }
            //LOGE("windows 1: %d %f %f %f %f", windowUpdater._longView, windows[0].xmin(), windows[0].ymin(), windows[0].xmax(), windows[0].ymax());
            stGestureInfo gestureinfo;
            if (gesture_mode())
            {
                std::vector<stGestureRecResult>& gestureresults = tracks.gestureResults();
                
                bool bges = gesture_process(gestureresults, gestureinfo);

                enGestureMode gesturemode = windowUpdater.process_window_with_gesture(tracks, windows, ratio, gestureinfo);  
                // if (gesturemode == EN_GESTURE_MODE_TRACK || gesturemode == EN_GESTURE_MODE_WHITEBOARD)
                // {
                //     LOGE("stop sub move_zoom: %d\n", gesturemode);
                //     TrackerInputOutput tracks_sub;
                //     fetch_tracks(tracks_sub, false);
                //     BboxF showwin_sub;
                //     std::vector<BboxF> windows_sub;
                //     windows_sub.push_back(windowUpdaterSub._last_move_zoom_win);
                //     stGestureInfo gestureinfo_sub = gestureinfo;
                //     windowUpdaterSub.update_move_zoom_state(tracks_sub, gesturemode, showwin_sub, windows_sub, gestureinfo_sub);
                // }            
            
                if (windowUpdater.getGestureEvent() != enGestureEventNone)
                {                         
                    if (_gesture_event_callback)
                    {
                        int ret = _gesture_event_callback(windowUpdater.getGestureEvent());
                        if (ret != 0)
                        {
                            LOGE("_gesture_event_callback failed: %d", ret);
                        }
                        LOGE("gesture_event: %d\n", windowUpdater.getGestureEvent());
                    }    

                    if (_camera_led_callback)
                    {
                        gettimeofday(&_last_led_time, NULL);
                        _led_blink_times = 10;
                        _led_blink_interval = 500;
                        _camera_led_callback(LED_MODE_BLINK);
                        _led_blink_times--;
                    }
                }

                if (_led_blink_times > 0)
                {
                    if (_camera_led_callback)
                    {
                        timeval nowtime;
                        gettimeofday(&nowtime, NULL);
                        float interval = 1000.0f * (nowtime.tv_sec - _last_led_time.tv_sec) + (nowtime.tv_usec - _last_led_time.tv_usec) / 1000.0f;
                        if (interval > _led_blink_interval)
                        {
                            _camera_led_callback(LED_MODE_BLINK);
                            _led_blink_times--;
                            gettimeofday(&_last_led_time, NULL);
                        }
                    }
                }
            }
            
            //LOGE("windows 2: %d %f %f %f %f", windowUpdater._longView, windows[0].xmin(), windows[0].ymin(), windows[0].xmax(), windows[0].ymax());
            std::vector<BboxF> windows_sub;
            
            TrackerInputOutput tracks_sub;
#ifndef _WIN32
            char device_model[PROPERTY_VALUE_MAX] = {0};
            property_get("persist.vendor.czur.hardware.model", device_model, "");
            
            bool device_studio = strcasecmp(device_model, "studio") == 0;
#else
            bool device_studio = false;
#endif
            if (false == device_studio)
            {
                fetch_tracks(tracks_sub, false);     

                std::vector<BboxF> in_bbox_sub = tracks_sub.in_bbox();

                for (auto & box: in_bbox_sub)
                {
                    box.xmin(box.xmin() * _down_scale_sub);
                    box.xmax(box.xmax() * _down_scale_sub);
                    box.ymin(box.ymin() * _down_scale_sub);
                    box.ymax(box.ymax() * _down_scale_sub);
                }  
                
                tracks_sub.in_bbox(in_bbox_sub);

                std::vector<BboxF> tracked_bbox_sub = tracks_sub.tracked_bbox();

                for (auto & box: tracked_bbox_sub)
                {
                    box.xmin(box.xmin() * _down_scale_sub);
                    box.xmax(box.xmax() * _down_scale_sub);
                    box.ymin(box.ymin() * _down_scale_sub);
                    box.ymax(box.ymax() * _down_scale_sub);
                }  
                
                tracks_sub.tracked_bbox(tracked_bbox_sub);
            }
            

            //std::vector<BboxF> windowsRaw = dets.bbox();
                                                                                                                                                           
            //float realdoa = -1;
            float doa_sub = doa;
            if (mode() == enTrackModeVideoandAudio)
            {
                if (doa_sub < 250 || doa_sub > 290)
                {
                    doa_sub = -1;
                }
            }           

            windowUpdaterSub.doa(doa_sub);
          
            //bool bges_sub = gesture_process(gestureresults_sub, gestureinfo_sub, true);
            if (mode() != enTrackModeNo)
            {
                windowUpdaterSub.process_window(tracks_sub, windows_sub, ratio, ModeVideoandAudio);
            }
            else
            {         
                if (gesture_mode())
                {
                    windowUpdaterSub.process_window(tracks_sub, windows_sub, ratio, ModeVideoandAudio);
                }   
                windows_sub.clear();   
                windows_sub.push_back(BboxF(0.0f, 0.0f, windowUpdater.frame_w() - 1,windowUpdater.frame_h() - 1));
            }

            if (gesture_mode())
            {
                //std::vector<stGestureRecResult>& gestureresults_sub = tracks_sub.gestureResults();
                stGestureInfo gestureinfo_sub = gestureinfo;

                gestureinfo_sub.box = windows_sub[0];

                std::vector<cv::Point2f> srcPoints;
                srcPoints.push_back(cv::Point2f(gestureinfo.box.xmin(), gestureinfo.box.ymin()));
                srcPoints.push_back(cv::Point2f(gestureinfo.box.xmin(), gestureinfo.box.ymax()));
                srcPoints.push_back(cv::Point2f(gestureinfo.box.xmax(), gestureinfo.box.ymin()));
                srcPoints.push_back(cv::Point2f(gestureinfo.box.xmax(), gestureinfo.box.ymax()));

                std::vector<cv::Point2f> dstPoints;                     
                cv::perspectiveTransform(srcPoints, dstPoints, windowUpdater._trans_short_to_long);

                gestureinfo_sub.box.xmin(dstPoints[0].x);
                gestureinfo_sub.box.ymin(dstPoints[0].y);
                gestureinfo_sub.box.xmax(dstPoints[3].x);
                gestureinfo_sub.box.ymax(dstPoints[3].y);
                enGestureMode mode_sub = gestureid2mode(gestureinfo_sub.gesture_id);
                //if (mode_sub == EN_GESTURE_MODE_TRACK && mode_sub != EN_GESTURE_MODE_WHITEBOARD)
                {
                    BboxF showwin_sub = windows_sub[0];
                    windowUpdaterSub.update_move_zoom_state(tracks_sub, mode_sub, showwin_sub, windows_sub, gestureinfo_sub, windowUpdater._zoom_move_type, 1.2f);
                    //enGestureMode gesturemode_sub = windowUpdaterSub.process_window_with_gesture(tracks_sub, windows_sub, ratio, gestureinfo_sub);
                    windowUpdaterSub._last_win = showwin_sub;
                }  
            }         

            stWindowInfo winInfo;
            if (windowUpdater._gesture_mode != EN_GESTURE_MODE_WHITEBOARD)
            {             
                winInfo.box = windows_sub[0];
                windowUpdater.setWinLongViewInfo(winInfo);    
            }
            
            if (!windowUpdater.in_stationary_gesture_state() )
            {
                if (windowUpdater._gesture_mode != EN_GESTURE_MODE_TRACK)
                {
                    windowUpdater.camera_change_process();
                }
                else
                {
                    windowUpdater._realbox = windowUpdater._realtrackbox; //windows[0];
                    windowUpdater.camera_change_process();

                    if (windowUpdater._longView == true)
                    {
                        BboxF box(0, 0, 0, 0);
                        for (auto& track: tracks.tracked_bbox())
                        {
                            int track_instance_id = track.instance_id();
                            if (windowUpdater._det_id_map.count(track_instance_id) > 0)
                            {
                                track_instance_id = windowUpdater._det_id_map[track_instance_id];
                            }
                            if (track_instance_id == windowUpdater._instace_id)
                            {
                                box = track;
                                break;
                            }
                        }                    
                        
                        //根据广角的框得到对应的长焦的id
                        //TrackerInputOutput tracks_sub;
                        std::vector<BboxF> windows_sub;
                        //fetch_tracks(tracks_sub, false);
                        int instance_id = -1;
                        float min_dist = FLT_MAX;
                        if (box.area() > 1)
                        {
                            std::vector<cv::Point2f> srcPoints;
                            srcPoints.push_back(cv::Point2f(box.xmin(), box.ymin()));
                            srcPoints.push_back(cv::Point2f(box.xmin(), box.ymax()));
                            srcPoints.push_back(cv::Point2f(box.xmax(), box.ymin()));
                            srcPoints.push_back(cv::Point2f(box.xmax(), box.ymax()));

                            std::vector<cv::Point2f> dstPoints;                     
                            cv::perspectiveTransform(srcPoints, dstPoints, windowUpdater._trans_short_to_long);

                            box.xmin(dstPoints[0].x);
                            box.ymin(dstPoints[0].y);
                            box.xmax(dstPoints[3].x);
                            box.ymax(dstPoints[3].y);
                            for (size_t n = 0;n < tracks_sub.tracked_bbox().size(); ++n)
                            {
                                auto& track = tracks_sub.tracked_bbox()[n];
                                float dist = track.distance_with(box);
                                if (dist < min_dist)
                                {
                                    min_dist = dist;
                                    instance_id = track.instance_id();
                                }
                            }
                        }

                        std::vector<BboxF> windowsOut;
                        bool bfind = windowUpdaterSub.process_person_track(tracks_sub, windowsOut, instance_id, false);

                        if (windowUpdaterSub._zoom_move_state != EN_ZOOM_MOVE_STATE_STOP)
                        {
                            windowUpdaterSub._zoom_move_state = EN_ZOOM_MOVE_STATE_IDLE;
                            windowUpdaterSub._last_move_zoom_win = windowUpdaterSub._gesture_zoom_move_win.box;
                        }
                        else
                        {
                            //windowUpdaterSub.get_output_window(box_sub, windowsOut, true);
                            windowUpdaterSub._last_move_zoom_win = windowsOut[0];
                            winInfo.box = windowsOut[0];
                            windowUpdater.setWinLongViewInfo(winInfo);
                        }  
                    }               
                }               
            } 

            _personViewerPtr->max_subwin_num(1);
            _personViewerPtr->preview_width(_previeww);
            _personViewerPtr->preview_height(_previewh);
            _personViewerPtr->out_width(out_width());
            _personViewerPtr->out_height(out_height()); 

            //LOGE("windows 3: %d %f %f %f %f", windowUpdater._longView, windows[0].xmin(), windows[0].ymin(), windows[0].xmax(), windows[0].ymax());
#ifndef _WIN32
            int view_mode = property_get_int32("vendor.czcv.camera.view", 0); //0:正常模式， 1：广角， 2：长焦     
#else
            int view_mode = 0;
#endif
            bool uselongView = false;
            if (phy_addr_sub() > 0) 
            {
                if (view_mode == 2)
                {
                    uselongView = true;
                }
                else if (view_mode != 1)
                {
                    if (windowUpdater._longView)
                    {
                        if (mode() != enTrackModeNo)
                        {
                            uselongView = true;
                        }
                        else
                        {
                            if (gesture_mode())
                            {
                                if (windowUpdater._gesture_mode == EN_GESTURE_MODE_TRACK)
                                {
                                    uselongView = true;
                                }
                                else if (windowUpdater.in_stationary_gesture_state())
                                {
                                    uselongView = true;
                                }
                            }
                        }
                    }            
                }
            }
            
            //uselongView = false;
            if (uselongView)
            {
                // windows[0].xmin(windowUpdater._winLongViewInfos[0].box.xmin());
                // windows[0].ymin(windowUpdater._winLongViewInfos[0].box.ymin());
                // windows[0].xmax(windowUpdater._winLongViewInfos[0].box.xmax());
                // windows[0].ymax(windowUpdater._winLongViewInfos[0].box.ymax()); 

                stWindowInfo winInfo;
                if(windowUpdater.getWinLongViewInfo(winInfo))
                {
                    windows[0].xmin(winInfo.box.xmin());
                    windows[0].ymin(winInfo.box.ymin());
                    windows[0].xmax(winInfo.box.xmax());
                    windows[0].ymax(winInfo.box.ymax()); 
                }
                else
                {
                    windows[0].xmin(0);
                    windows[0].ymin(0);
                    windows[0].xmax(_previeww - 1);
                    windows[0].ymax(_previewh - 1); 
                }

                if ((int)(windows[0].xmin()) < 0 || (int)(windows[0].ymin()) < 0 || (int)(windows[0].xmax()) > (_previeww - 1) ||  (int)(windows[0].ymax()) > (_previewh - 1))
                {
                    LOGE("windows long view: %f %f %f %f", windows[0].xmin(), windows[0].ymin(), windows[0].xmax(), windows[0].ymax());
                }

                windows[0] = windows[0].clip_by(0, _previeww - 1, 0, _previewh - 1);

                if(mode() == enTrackModeVideoandAudio && windowUpdater.is_speaking() && windowUpdater._gesture_mode == EN_GESTURE_MODE_NONE)
                {
                    BboxF& box = windows[0];
                    box.ymax(box.ymin() + box.height()  * 5 / 7);
                    // float target_ratio = _previeww * 1.0f / (_previewh * 2 / 3);
                    
                    // float current_width = box.width();
                    // float current_height = box.height();
                    // float current_ratio = current_width / current_height;

                    // if (current_ratio < target_ratio) {
                    //     // Need to increase width
                    //     float new_width = current_height * target_ratio;
                    //     float expand_each_side = (new_width - current_width) / 2;
                        
                    //     // Try to expand equally on both sides
                    //     float left_expand = std::min(expand_each_side, box.xmin());
                    //     float right_expand = std::min(expand_each_side, _previeww - 1 - box.xmax());
                        
                    //     box.xmin(box.xmin() - left_expand);
                    //     box.xmax(box.xmax() + right_expand);
                        
                    //     // If couldn't expand enough, need to reduce height from bottom
                    //     if (box.width() / box.height() < target_ratio) {
                    //         float required_height = box.width() / target_ratio;
                    //         box.ymax(box.ymin() + required_height);
                    //     }
                    // } else {
                    //     // Need to reduce height from bottom
                    //     float new_height = current_width / target_ratio;
                    //     box.ymax(box.ymin() + new_height);
                    // }
                    
                    // // Ensure box stays within valid bounds
                    // box.xmin(std::max(0.0f, box.xmin()));
                    // box.xmax(std::min(float(_previeww - 1), box.xmax()));
                    // box.ymin(std::max(0.0f, box.ymin()));
                    // box.ymax(std::min(float(_previewh - 1), box.ymax()));

                    _personViewerPtr->on_process_frame_roi(_frame, frame, windows, phy_addr_sub(), dst_vir_addr(), dst_phy_addr());
                }
                else
                {
                    // windows[0].xmin(0);
                    // windows[0].ymin(0);
                    // windows[0].xmax(3840 - 1);
                    // windows[0].ymax(2160 - 1);

                    _personViewerPtr->on_process_frame(_frame, frame, windows, phy_addr_sub(), dst_vir_addr(), dst_phy_addr());
                }    
            }
            else
            {         
                if ((int)(windows[0].xmin()) < 0 || (int)(windows[0].ymin()) < 0 || (int)(windows[0].xmax()) > (_previeww - 1) ||  (int)(windows[0].ymax()) > (_previewh - 1))
                {
                    LOGE("windows not long view: %f %f %f %f", windows[0].xmin(), windows[0].ymin(), windows[0].xmax(), windows[0].ymax());
                }

                windows[0] = windows[0].clip_by(0, _previeww - 1, 0, _previewh - 1);

                if (mode() == enTrackModeVideoandAudio && windowUpdater.is_speaking() && windowUpdater._gesture_mode == EN_GESTURE_MODE_NONE)
                {
                    BboxF& box = windows[0];
                    box.ymax(box.ymin() + box.height() * 5 / 7);
                    // float target_ratio = _previeww * 1.0f / (_previewh * 2 / 3);
                    // BboxF& box = windows[0];
                    // float current_width = box.width();
                    // float current_height = box.height();
                    // float current_ratio = current_width / current_height;

                    // if (current_ratio < target_ratio) {
                    //     // Need to increase width
                    //     float new_width = current_height * target_ratio;
                    //     float expand_each_side = (new_width - current_width) / 2;
                        
                    //     // Try to expand equally on both sides
                    //     float left_expand = std::min(expand_each_side, box.xmin());
                    //     float right_expand = std::min(expand_each_side, _previeww - 1 - box.xmax());
                        
                    //     box.xmin(box.xmin() - left_expand);
                    //     box.xmax(box.xmax() + right_expand);
                        
                    //     // If couldn't expand enough, need to reduce height from bottom
                    //     if (box.width() / box.height() < target_ratio) {
                    //         float required_height = box.width() / target_ratio;
                    //         box.ymax(box.ymin() + required_height);
                    //     }
                    // } else {
                    //     // Need to reduce height from bottom
                    //     float new_height = current_width / target_ratio;
                    //     box.ymax(box.ymin() + new_height);
                    // }
                    
                    // // Ensure box stays within valid bounds
                    // box.xmin(std::max(0.0f, box.xmin()));
                    // box.xmax(std::min(float(_previeww - 1), box.xmax()));
                    // box.ymin(std::max(0.0f, box.ymin()));
                    // box.ymax(std::min(float(_previewh - 1), box.ymax()));

                    _personViewerPtr->on_process_frame_roi(_frame, frame, windows, phy_addr(), dst_vir_addr(), dst_phy_addr());     
                }      
                else
                {
                    if (mode() == enTrackModeNo && gesture_mode() == false)
                    {                 
                        windows[0].xmin(0);
                        windows[0].ymin(0);
                        windows[0].xmax(windowUpdater.frame_w() - 1);
                        windows[0].ymax(windowUpdater.frame_h() - 1); 
                    }          

                    // windows[0].xmin(0);
                    // windows[0].ymin(0);
                    // windows[0].xmax(windowUpdater.frame_w() - 1);
                    // windows[0].ymax(windowUpdater.frame_h() - 1); 

                    // windows[0].xmin(1550);
                    // windows[0].ymin(556);
                    // windows[0].xmax(1550 + 1940);
                    // windows[0].ymax(556 + 1090);

                    _personViewerPtr->on_process_frame(_frame, frame, windows, phy_addr(), dst_vir_addr(), dst_phy_addr()); 
                }            
            }  

            if (windowUpdater._gesture_mode == EN_GESTURE_MODE_NONE && mode() == enTrackModeVideoandAudio && windowUpdater.is_speaking())
            {
                if (CZCV_OK == (int)opencl_run())
                {
                    int strip_y = (out_height() -  out_height() *  2 / 7) - 5 * out_height() / 1080;
                    strip_y = strip_y - (strip_y & 1);
                    int strip_height = 10 * out_height() / 1080;
                    strip_height = strip_height + (strip_height & 1);
                    int ret = _rgaops->cropScaleRoi(_mblack_strip.phyaddr, dst_phy_addr(), ((_previeww + 7) & (~7)), 16, 0, 0, ((_previeww + 7) & (~7)), 16, out_width(), out_height(), 0, strip_y, out_width(), strip_height);    
                    if (ret != 0)
                    {
                        LOGE("RGA cropScaleRoi failed: %d", ret);
                    }
                    // if (fid < 20)
                    // {
                    //     FILE* f= fopen(("/mnt/imgs/" + std::to_string(fid++) + ".bin").c_str(), "wb");
                    //     if (f)
                    //     {
                    //         fwrite(dst_vir_addr(), 1,out_width() * out_height() * 3 / 2, f);
                    //         fclose(f);
                    //     }
                    // }
                    
                }
            }
 
#ifndef _WIN32
            int debug_mode = property_get_int32("vendor.czcv.camera.debug", 0);
#else
            int debug_mode = 0;
#endif
            if (debug_mode)
            {
//#if 1
                cv::Mat debug(out_height(), out_width(), CV_8UC1, dst_vir_addr());
                if(windowUpdater._longView)
                {
                    //TrackerInputOutput tracks;
                    //fetch_tracks(tracks, false);
                    std::vector<stGestureRecResult>& gestureresults = tracks_sub.gestureResults();

                    BboxF outbox = windows[0];
                    std::vector<cv::Point2f> srcPoints;
                    std::vector<cv::Point2f> dstPoints;

                    srcPoints.push_back(cv::Point2f(outbox.xmin(), outbox.ymin()));
                    srcPoints.push_back(cv::Point2f(outbox.xmin(), outbox.ymax()));
                    srcPoints.push_back(cv::Point2f(outbox.xmax(), outbox.ymin()));

                    dstPoints.push_back(cv::Point2f(0, 0));
                    dstPoints.push_back(cv::Point2f(0, out_height() - 1));
                    dstPoints.push_back(cv::Point2f(out_width() - 1, 0));

                    cv::Mat M = cv::getAffineTransform(srcPoints, dstPoints); 

                    for (auto& result : gestureresults)
                    {
                        NormalizedRect rect = result.rect;
                        //std::vector<cv::Point3f> landmarks = result.landmarks;
                        int clsid = result.clsid;
                        // int x = (int)((rect.x_center - rect.width / 2) * out_width());
                        // int y = (int)((rect.y_center - rect.height / 2) * out_height());

                        Rectf rectf = result.rectf;
                        int x = rectf.x0;
                        int y = rectf.y0;

                        std::vector<cv::Point2f> srcPoints;
                        srcPoints.push_back(cv::Point2f(x, y));
                        srcPoints.push_back(cv::Point2f(rectf.x1, rectf.y1));

                        std::vector<cv::Point2f> dstPoints;
                        cv::transform(srcPoints, dstPoints, M);
            
                        cv::putText(debug, std::to_string(clsid), cv::Point2i(dstPoints[0].x, dstPoints[0].y), cv::FONT_HERSHEY_SIMPLEX, 3, cv::Scalar(0, 0, 0), 3);
                        cv::rectangle(debug, cv::Point(dstPoints[0].x, dstPoints[0].y), cv::Point(dstPoints[1].x, dstPoints[1].y), cv::Scalar(255, 255, 255), 3); 
                    }

                    for (auto& box : tracks_sub.in_bbox())
                    {
                        std::vector<cv::Point2f> srcPoints;
                        srcPoints.push_back(cv::Point2f(box.xmin(), box.ymin()));
                        srcPoints.push_back(cv::Point2f(box.xmax(), box.ymax()));

                        std::vector<cv::Point2f> dstPoints;
                        cv::transform(srcPoints, dstPoints, M);

                        cv::rectangle(debug, cv::Point(dstPoints[0].x, dstPoints[0].y), cv::Point(dstPoints[1].x, dstPoints[1].y), cv::Scalar(255, 255, 255), 3);   
                        
                        int instance_id = box.instance_id();
                        if (windowUpdater._det_id_map.count(instance_id) > 0)
                        {
                            instance_id = windowUpdater._det_id_map[instance_id];
                        }
    
                        cv::putText(debug, std::to_string(instance_id), cv::Point(dstPoints[0].x + 10, dstPoints[0].y + 10), cv::FONT_HERSHEY_SIMPLEX, 2, cv::Scalar(0, 0, 0), 3);
                    }

                    int mapidx = 0;
                    for (const auto& pair : windowUpdater._det_id_map)
                    {
                        cv::putText(debug, std::to_string(pair.first), cv::Point(out_width() - 200, 100 + mapidx * 200), cv::FONT_HERSHEY_SIMPLEX, 2, cv::Scalar(0, 0, 0), 3);
                        cv::putText(debug, std::to_string(pair.second), cv::Point(out_width() - 400, 100 + mapidx * 200), cv::FONT_HERSHEY_SIMPLEX, 2, cv::Scalar(0, 0, 0), 3);
                        mapidx++;
                    }
                }
                else
                {  
                    std::vector<int> angles = {220,230,240,250,260,270,280,290,300,310,320};
                    float INV_SQRT_3 = 0.75355f;
                    float SQRT_3 = 1.327f; //tan(53)
                    float PI = 3.141592653f;

                    // INV_SQRT_3 = 2.1445f;
                    // SQRT_3 = 0.4663f; //tan(25)

                    float realdoa;
                    read_doa(&realdoa);

                    for (auto angle : angles)
                    {
                        int xcoor_debug = (-tanf((angle- 270) * PI / 180) * INV_SQRT_3 * out_width() * 0.5f + out_width() * 0.5f);
                        // cv::circle(debug,cv::Point2d(xcoor_debug,100),5,cv::Scalar(0,0,0),2);
                        if ((int)realdoa == (int)angle)
                        {
                            cv::line(debug,cv::Point2i(xcoor_debug,0),cv::Point2i(xcoor_debug,out_height()-1),cv::Scalar(0,0,0),4);
                        }
                        else
                        {
                            cv::line(debug,cv::Point2i(xcoor_debug,0),cv::Point2i(xcoor_debug,out_height()-1),cv::Scalar(0,0,0),2);
                        }
                        
                        cv::putText(debug, std::to_string(angle), cv::Point2i(xcoor_debug-50, 50), cv::FONT_HERSHEY_SIMPLEX, 1.5, cv::Scalar(0, 0, 0), 2);
                    }
                    cv::putText(debug, std::to_string((int)realdoa), cv::Point(200, 100), cv::FONT_HERSHEY_SIMPLEX, 2, cv::Scalar(0, 0, 0), 3); 

                    // BboxF outbox = windows[0];
                    // std::vector<cv::Point2f> srcPoints;
                    // std::vector<cv::Point2f> dstPoints;

                    // srcPoints.push_back(cv::Point2f(outbox.xmin(), outbox.ymin()));
                    // srcPoints.push_back(cv::Point2f(outbox.xmin(), outbox.ymax()));
                    // srcPoints.push_back(cv::Point2f(outbox.xmax(), outbox.ymin()));

                    // dstPoints.push_back(cv::Point2f(0, 0));
                    // dstPoints.push_back(cv::Point2f(0, out_height() - 1));
                    // dstPoints.push_back(cv::Point2f(out_width() - 1, 0));

                    // cv::Mat M = cv::getAffineTransform(srcPoints, dstPoints); 
                                   
                    // std::vector<stGestureRecResult>& gestureresults = tracks.gestureResults();
                    // for (auto& result : gestureresults)
                    // {
                    //     //NormalizedRect rect = result.rect;
                    //     //std::vector<cv::Point3f> landmarks = result.landmarks;
                    //     int clsid = result.clsid;
                    //     // int x = (int)((rect.x_center - rect.width / 2) * out_width());
                    //     // int y = (int)((rect.y_center - rect.height / 2) * out_height());

                    //     Rectf rectf = result.rectf;
                    //     int x = rectf.x0;
                    //     int y = rectf.y0;

                    //     std::vector<cv::Point2f> srcPoints;
                    //     srcPoints.push_back(cv::Point2f(x, y));
                    //     srcPoints.push_back(cv::Point2f(rectf.x1, rectf.y1));

                    //     std::vector<cv::Point2f> dstPoints;
                    //     cv::transform(srcPoints, dstPoints, M);
            
                    //     cv::putText(debug, std::to_string(clsid), cv::Point2i(dstPoints[0].x, dstPoints[0].y), cv::FONT_HERSHEY_SIMPLEX, 3, cv::Scalar(0, 0, 0), 3);
                    //     cv::rectangle(debug, cv::Point(dstPoints[0].x, dstPoints[0].y), cv::Point(dstPoints[1].x, dstPoints[1].y), cv::Scalar(255, 255, 255), 3);
                    // }

                    // for (auto& box : tracks.tracked_bbox())
                    // {
                    //     // if (box.class_id() != 0)
                    //     // {
                    //     //     continue;
                    //     // }
                    //     std::vector<cv::Point2f> srcPoints;
                    //     srcPoints.push_back(cv::Point2f(box.xmin(), box.ymin()));
                    //     srcPoints.push_back(cv::Point2f(box.xmax(), box.ymax()));

                    //     std::vector<cv::Point2f> dstPoints;
                    //     cv::transform(srcPoints, dstPoints, M);

                    //     cv::rectangle(debug, cv::Point(dstPoints[0].x, dstPoints[0].y), cv::Point(dstPoints[1].x, dstPoints[1].y), cv::Scalar(255, 255, 255), 3);   
                        
                    //     int instance_id = box.instance_id();
                    //     if (windowUpdater._det_id_map.count(instance_id) > 0)
                    //     {
                    //         instance_id = windowUpdater._det_id_map[instance_id];
                    //     }
    
                    //     cv::putText(debug, std::to_string(instance_id), cv::Point(dstPoints[0].x + 10, dstPoints[0].y + 10), cv::FONT_HERSHEY_SIMPLEX, 2, cv::Scalar(0, 0, 0), 3);
                    // }           
                    
                    // int mapidx = 0;
                    // for (const auto& pair : windowUpdater._det_id_map)
                    // {
                    //     cv::putText(debug, std::to_string(pair.first), cv::Point(out_width() - 200, 100 + mapidx * 200), cv::FONT_HERSHEY_SIMPLEX, 2, cv::Scalar(0, 0, 0), 3);
                    //     cv::putText(debug, std::to_string(pair.second), cv::Point(out_width() - 400, 100 + mapidx * 200), cv::FONT_HERSHEY_SIMPLEX, 2, cv::Scalar(0, 0, 0), 3);
                    //     mapidx++;
                    // }

                    // cv::rectangle(debug, cv::Point(windowUpdater.longview.xmin(), windowUpdater.longview.ymin()), cv::Point(windowUpdater.longview.xmax(), windowUpdater.longview.ymax()), cv::Scalar(0, 0, 0), 3);               
                    
                    //cv::putText(debug, std::to_string(windowUpdater.g_debug_mode), cv::Point(out_width() - 200, 100), cv::FONT_HERSHEY_SIMPLEX, 2, cv::Scalar(0, 0, 0), 3);
                    //cv::putText(debug, std::to_string(windowUpdater._gesture_mode), cv::Point(out_width() - 200, 300), cv::FONT_HERSHEY_SIMPLEX, 2, cv::Scalar(0, 0, 0), 3);
                    //cv::putText(debug, std::to_string(windowUpdater._bwhite_board), cv::Point(out_width() - 200, 500), cv::FONT_HERSHEY_SIMPLEX, 2, cv::Scalar(0, 0, 0), 3);  
                    //cv::putText(debug, std::to_string(windowUpdater.debug_gesturemode), cv::Point(out_width() - 200, 500), cv::FONT_HERSHEY_SIMPLEX, 2, cv::Scalar(0, 0, 0), 3);        
                    //cv::putText(debug, std::to_string(windowUpdater.getGestureEvent()), cv::Point(out_width() - 200, 700), cv::FONT_HERSHEY_SIMPLEX, 2, cv::Scalar(0, 0, 0), 3);        
                    //cv::putText(debug, std::to_string(windowUpdater._zoom_move_type), cv::Point(out_width() - 200, 900), cv::FONT_HERSHEY_SIMPLEX, 2, cv::Scalar(0, 0, 0), 3);   
                    //cv::putText(debug, std::to_string(windowUpdater._zoom_move_state), cv::Point(out_width() - 200, 1100), cv::FONT_HERSHEY_SIMPLEX, 2, cv::Scalar(0, 0, 0), 3); 
                    // cv::putText(debug, std::to_string((int)(windowUpdater._last_move_zoom_win.xmin())), cv::Point(out_width() - 200, 900), cv::FONT_HERSHEY_SIMPLEX, 2, cv::Scalar(255, 255, 255), 3);  
                    // cv::putText(debug, std::to_string((int)(windowUpdater._last_move_zoom_win.ymin())), cv::Point(out_width() - 400, 900), cv::FONT_HERSHEY_SIMPLEX, 2, cv::Scalar(255, 255, 255), 3);  
                    // cv::putText(debug, std::to_string((int)(windowUpdater._last_move_zoom_win.xmax())), cv::Point(out_width() - 600, 900), cv::FONT_HERSHEY_SIMPLEX, 2, cv::Scalar(255, 255, 255), 3);  
                    // cv::putText(debug, std::to_string((int)(windowUpdater._last_move_zoom_win.ymax())), cv::Point(out_width() - 800, 900), cv::FONT_HERSHEY_SIMPLEX, 2, cv::Scalar(255, 255, 255), 3);  
                    // cv::putText(debug, std::to_string((int)(gestureinfo.box.xmin())), cv::Point(out_width() - 200, 1100), cv::FONT_HERSHEY_SIMPLEX, 2, cv::Scalar(255, 255, 255), 3);  
                    // cv::putText(debug, std::to_string((int)(gestureinfo.box.ymin())), cv::Point(out_width() - 400, 1100), cv::FONT_HERSHEY_SIMPLEX, 2, cv::Scalar(255, 255, 255), 3);  
                    // cv::putText(debug, std::to_string((int)(gestureinfo.box.xmax())), cv::Point(out_width() - 600, 1100), cv::FONT_HERSHEY_SIMPLEX, 2, cv::Scalar(255, 255, 255), 3);  
                    // cv::putText(debug, std::to_string((int)(gestureinfo.box.ymax())), cv::Point(out_width() - 800, 1100), cv::FONT_HERSHEY_SIMPLEX, 2, cv::Scalar(255, 255, 255), 3); 
                    
                    //cv::putText(debug, std::to_string(appro), cv::Point(out_width() - 200, 1100), cv::FONT_HERSHEY_SIMPLEX, 2, cv::Scalar(0, 0, 0), 3);
                    //cv::putText(debug, std::to_string(bin), cv::Point(out_width() - 200, 1300), cv::FONT_HERSHEY_SIMPLEX, 2, cv::Scalar(0, 0, 0), 3);            
                }

    //#endif

                //显示一些必要信息
                if (0) //(windowUpdater._longView == false)
                {
                    cv::Mat debug(out_height(), out_width(), CV_8UC1, dst_vir_addr());
                    BboxF outbox = windows[0];
                    std::vector<cv::Point2f> srcPoints;
                    std::vector<cv::Point2f> dstPoints;

                    srcPoints.push_back(cv::Point2f(outbox.xmin(), outbox.ymin()));
                    srcPoints.push_back(cv::Point2f(outbox.xmin(), outbox.ymax()));
                    srcPoints.push_back(cv::Point2f(outbox.xmax(), outbox.ymin()));

                    dstPoints.push_back(cv::Point2f(0, 0));
                    dstPoints.push_back(cv::Point2f(0, out_height() - 1));
                    dstPoints.push_back(cv::Point2f(out_width() - 1, 0));
                    cv::Mat M = cv::getAffineTransform(srcPoints, dstPoints);

                    srcPoints.clear();
                    srcPoints.push_back(cv::Point2f(windowUpdater.longview.xmin(), windowUpdater.longview.ymin()));
                    srcPoints.push_back(cv::Point2f(windowUpdater.longview.xmin(), windowUpdater.longview.ymax()));
                    srcPoints.push_back(cv::Point2f(windowUpdater.longview.xmax(), windowUpdater.longview.ymin()));
                    srcPoints.push_back(cv::Point2f(windowUpdater.longview.xmax(), windowUpdater.longview.ymax()));

                    cv::transform(srcPoints, dstPoints, M);
                    //LOGE("dstPoints: %f %f %f %f %f %f %f %f\n", dstPoints[0].x, dstPoints[0].y, dstPoints[1].x, dstPoints[1].y, dstPoints[2].x, dstPoints[2].y, dstPoints[3].x, dstPoints[3].y);
                    cv::rectangle(debug, dstPoints[0], dstPoints[3], cv::Scalar(0, 0, 0), 3); 
                    //float realdoa;
                    //read_doa(&realdoa);
                    //cv::putText(debug, std::to_string((int)realdoa), cv::Point(200, 100), cv::FONT_HERSHEY_SIMPLEX, 2, cv::Scalar(0, 0, 0), 3);           
                }

            }

            
            // //std::vector<int> angles = {220,230,240,250,260,270,280,290,300,310,320};
            // std::vector<int> angles = {245,250,260,270,280,290,295};
            // float INV_SQRT_3 = 0.75355f;
            // float SQRT_3 = 1.327f; //tan(53)
            // float PI = 3.141592653f;

            // // INV_SQRT_3 = 2.1445f;
            // // SQRT_3 = 0.4663f; //tan(25)

            // for (auto angle : angles)
            // {
            //     int xcoor_debug = (-tanf((angle- 270) * PI / 180) * INV_SQRT_3 * out_width() * 0.5f + out_width() * 0.5f);
            //     // cv::circle(debug,cv::Point2d(xcoor_debug,100),5,cv::Scalar(0,0,0),2);
            //     cv::line(debug,cv::Point2i(xcoor_debug,0),cv::Point2i(xcoor_debug,out_height()-1),cv::Scalar(0,0,0),2);
            //     cv::putText(debug, std::to_string(angle), cv::Point2i(xcoor_debug-50, 50), cv::FONT_HERSHEY_SIMPLEX, 2, cv::Scalar(0, 0, 0), 2);
            // }

            // cv::putText(debug, std::to_string((int)_tmpdoa), cv::Point(200, 100), cv::FONT_HERSHEY_SIMPLEX, 2, cv::Scalar(0, 0, 0), 3);
 
            //float realdoa;
            //read_doa(&realdoa);
            //cv::putText(debug, std::to_string((int)realdoa), cv::Point(200, 100), cv::FONT_HERSHEY_SIMPLEX, 2, cv::Scalar(0, 0, 0), 3);
            // cv::putText(debug, std::to_string(detection_doa), cv::Point(out_width() - 200, 700), cv::FONT_HERSHEY_SIMPLEX, 1, cv::Scalar(0, 0, 0), 2);
            // cv::putText(debug, std::to_string(speak_size), cv::Point(out_width() - 200, 500), cv::FONT_HERSHEY_SIMPLEX, 2, cv::Scalar(0, 0, 0), 3);
            // cv::putText(debug, std::to_string(detect_size), cv::Point(out_width() - 200, 300), cv::FONT_HERSHEY_SIMPLEX, 2, cv::Scalar(0, 0, 0), 3);
            // cv::putText(debug, _mov, cv::Point(out_width() - 200, 100), cv::FONT_HERSHEY_SIMPLEX, 2, cv::Scalar(0, 0, 0), 3);
            
            // cv::putText(debug, std::to_string(static_total), cv::Point(out_width() - 300, 400), cv::FONT_HERSHEY_SIMPLEX, 2, cv::Scalar(0, 0, 0), 3);
            // int x_1 = 200;
            // int x_2 = 200;
            // for (int i=0;i<new_id.size();i++)
            // {
            //     x_1 += 100;
            //     cv::putText(debug, std::to_string(new_id[i]), cv::Point(out_width() - x_1, 100), cv::FONT_HERSHEY_SIMPLEX, 2, cv::Scalar(0, 0, 0), 3);
            // }
            // for (int i=0;i<old_id.size();i++)
            // {
            //     x_2 += 100;
            //     cv::putText(debug, std::to_string(old_id[i]), cv::Point(out_width() - x_2, 300), cv::FONT_HERSHEY_SIMPLEX, 2, cv::Scalar(0, 0, 0), 3);
            // }
            // cv::putText(debug, std::to_string(filter_size), cv::Point(out_width() - 300, 500), cv::FONT_HERSHEY_SIMPLEX, 2, cv::Scalar(0, 0, 0), 3);
            // BboxF outbox = windows[0];
            // std::vector<cv::Point2f> srcPoints;
            // std::vector<cv::Point2f> dstPoints;

            // srcPoints.push_back(cv::Point2f(outbox.xmin(), outbox.ymin()));
            // srcPoints.push_back(cv::Point2f(outbox.xmin(), outbox.ymax()));
            // srcPoints.push_back(cv::Point2f(outbox.xmax(), outbox.ymin()));

            // dstPoints.push_back(cv::Point2f(0, 0));
            // dstPoints.push_back(cv::Point2f(0, out_height() - 1));
            // dstPoints.push_back(cv::Point2f(out_width() - 1, 0));

            // cv::Mat M = cv::getAffineTransform(srcPoints, dstPoints);
            // for (auto & box: debug_boxs)
            // {              
            //     std::vector<cv::Point2f> srcPoints;
            //     srcPoints.push_back(cv::Point2f(box.xmin(), box.ymin()));
            //     srcPoints.push_back(cv::Point2f(box.xmin(), box.ymax()));
            //     srcPoints.push_back(cv::Point2f(box.xmax(), box.ymin()));
            //     srcPoints.push_back(cv::Point2f(box.xmax(), box.ymax()));

            //     std::vector<cv::Point2f> dstPoints;
            //     cv::transform(srcPoints, dstPoints, M);
            //     cv::putText(debug, std::to_string(box.height()), cv::Point2f(dstPoints[0].x,dstPoints[0].y+100), cv::FONT_HERSHEY_SIMPLEX, 2, cv::Scalar(0, 0, 0), 3);
            //     cv::putText(debug, std::to_string(dstPoints[0].y), cv::Point2f(dstPoints[0].x,dstPoints[0].y+200), cv::FONT_HERSHEY_SIMPLEX, 2, cv::Scalar(0, 0, 0), 3);
            //     cv::rectangle(debug, dstPoints[0], dstPoints[3], cv::Scalar(255, 255, 255), 3);   
            // }
            

#if DEBUG_SHOW_MODE
            cv::Mat debug(out_height(), out_width(), CV_8UC1, dst_vir_addr());
            BboxF outbox = windows[0];
            std::vector<cv::Point2f> srcPoints;
            std::vector<cv::Point2f> dstPoints;

            srcPoints.push_back(cv::Point2f(outbox.xmin(), outbox.ymin()));
            srcPoints.push_back(cv::Point2f(outbox.xmin(), outbox.ymax()));
            srcPoints.push_back(cv::Point2f(outbox.xmax(), outbox.ymin()));

            dstPoints.push_back(cv::Point2f(0, 0));
            dstPoints.push_back(cv::Point2f(0, out_height() - 1));
            dstPoints.push_back(cv::Point2f(out_width() - 1, 0));
            cv::Mat M = cv::getAffineTransform(srcPoints, dstPoints);
            
            if (windowUpdater._longView == false)
            {                            
                srcPoints.clear();
                srcPoints.push_back(cv::Point2f(windowUpdater.longview.xmin(), windowUpdater.longview.ymin()));
                srcPoints.push_back(cv::Point2f(windowUpdater.longview.xmin(), windowUpdater.longview.ymax()));
                srcPoints.push_back(cv::Point2f(windowUpdater.longview.xmax(), windowUpdater.longview.ymin()));
                srcPoints.push_back(cv::Point2f(windowUpdater.longview.xmax(), windowUpdater.longview.ymax()));

                cv::transform(srcPoints, dstPoints, M);
                //LOGE("dstPoints: %f %f %f %f %f %f %f %f\n", dstPoints[0].x, dstPoints[0].y, dstPoints[1].x, dstPoints[1].y, dstPoints[2].x, dstPoints[2].y, dstPoints[3].x, dstPoints[3].y);
                cv::rectangle(debug, dstPoints[0], dstPoints[3], cv::Scalar(0, 0, 0), 3);    
            
                for (auto & box: tracks.in_bbox())
                {              
                    std::vector<cv::Point2f> srcPoints;
                    srcPoints.push_back(cv::Point2f(box.xmin(), box.ymin()));
                    srcPoints.push_back(cv::Point2f(box.xmin(), box.ymax()));
                    srcPoints.push_back(cv::Point2f(box.xmax(), box.ymin()));
                    srcPoints.push_back(cv::Point2f(box.xmax(), box.ymax()));

                    std::vector<cv::Point2f> dstPoints;
                    cv::transform(srcPoints, dstPoints, M);

                    if (find(speak_id_show.begin(), speak_id_show.end(), box.instance_id()) == speak_id_show.end())
                    {
                        cv::rectangle(debug, dstPoints[0], dstPoints[3], cv::Scalar(127, 127, 127), 3);
                    }
                    else
                    {
                        cv::rectangle(debug, dstPoints[0], dstPoints[3], cv::Scalar(255, 255, 255), 3);  
                    }
                                   
                }

                if(state_show)
                {
                    cv::putText(debug, std::string("S"), cv::Point(out_width() - 200, 200), cv::FONT_HERSHEY_SIMPLEX, 2, cv::Scalar(0, 0, 0), 3); 
                }
                else
                {
                    cv::putText(debug, std::string("D"), cv::Point(out_width() - 200, 200), cv::FONT_HERSHEY_SIMPLEX, 2, cv::Scalar(0, 0, 0), 3); 
                }
            }
            else
            {
                TrackerInputOutput tracks;
                fetch_tracks(tracks, false);
                for (auto & box: tracks.in_bbox())
                {              
                    std::vector<cv::Point2f> srcPoints;
                    srcPoints.push_back(cv::Point2f(box.xmin(), box.ymin()));
                    srcPoints.push_back(cv::Point2f(box.xmin(), box.ymax()));
                    srcPoints.push_back(cv::Point2f(box.xmax(), box.ymin()));
                    srcPoints.push_back(cv::Point2f(box.xmax(), box.ymax()));

                    std::vector<cv::Point2f> dstPoints;
                    cv::transform(srcPoints, dstPoints, M);
  
                    cv::rectangle(debug, dstPoints[0], dstPoints[3], cv::Scalar(255, 255, 255), 3);                       
                }
            }

            cv::putText(debug, std::to_string((int)_tmpdoa), cv::Point(200, 100), cv::FONT_HERSHEY_SIMPLEX, 2, cv::Scalar(0, 0, 0), 3);
            
            // for (auto & box: tracks.in_bbox())
            // {              
            //     std::vector<cv::Point2f> srcPoints;
            //     srcPoints.push_back(cv::Point2f(box.xmin(), box.ymin()));
            //     srcPoints.push_back(cv::Point2f(box.xmin(), box.ymax()));
            //     srcPoints.push_back(cv::Point2f(box.xmax(), box.ymin()));
            //     srcPoints.push_back(cv::Point2f(box.xmax(), box.ymax()));

            //     std::vector<cv::Point2f> dstPoints;
            //     cv::transform(srcPoints, dstPoints, M);
            //     cv::putText(debug, std::to_string(box.class_id()), dstPoints[0], cv::FONT_HERSHEY_SIMPLEX, 2, cv::Scalar(0, 0, 0), 3);
            //     if (find(speak_id_show.begin(), speak_id_show.end(), box.instance_id()) == speak_id_show.end())
            //     {
            //         cv::rectangle(debug, dstPoints[0], dstPoints[3], cv::Scalar(0, 0, 0), 3);
            //     }
            //     else{cv::rectangle(debug, dstPoints[0], dstPoints[3], cv::Scalar(255, 255, 255), 3);}
                    
            // }

            //显示doa实时角度
            //cv::putText(debug, std::to_string((int)realdoa), cv::Point(out_width() - 200, 100), cv::FONT_HERSHEY_SIMPLEX, 2, cv::Scalar(0, 0, 0), 3); 
            //显示状态

            // //显示被遮挡的doa发声方向
            // for(int i=0;i<covered_doas_show.size();i++)
            // {
            //     cv::putText(debug, std::to_string((int)covered_doas_show[i]), cv::Point(out_width() - 200-200*i, 300), cv::FONT_HERSHEY_SIMPLEX, 2, cv::Scalar(0, 0, 0), 3);
            // }
            // //显示检测框的个数
            // cv::putText(debug, std::to_string(detect_nums), cv::Point(out_width() - 200, 400), cv::FONT_HERSHEY_SIMPLEX, 2, cv::Scalar(0, 0, 0), 3);
            // //显示doa与检测的时间差
            // //double tttt= 1000.0 * (present_time.tv_sec - detect_time.tv_sec) + (present_time.tv_usec - detect_time.tv_usec) / 1000.0;
            // //显示检测时刻对应的doa
            // //cv::putText(debug, std::to_string(detection_doa), cv::Point(out_width() - 200, 500), cv::FONT_HERSHEY_SIMPLEX, 2, cv::Scalar(0, 0, 0), 3);

            // for(int i=0;i<id_show.size();i++)
            // {
            //     cv::putText(debug, std::to_string(id_show[i]), cv::Point(out_width() - 200-200*i, 500), cv::FONT_HERSHEY_SIMPLEX, 2, cv::Scalar(0, 0, 0), 3);
            // }
            // //静态时，id的匹配情况
            // cv::putText(debug, std::string("T"), cv::Point(out_width() - 200, 600), cv::FONT_HERSHEY_SIMPLEX, 2, cv::Scalar(0, 0, 0), 3);
            // for(int i=0;i<unmatched_track_id_show.size();i++)
            // {
            //     cv::putText(debug, std::to_string(unmatched_track_id_show[i]), cv::Point(out_width() - 400-200*i, 600), cv::FONT_HERSHEY_SIMPLEX, 2, cv::Scalar(0, 0, 0), 3);
            // }
            // cv::putText(debug, std::string("S"), cv::Point(out_width() - 200, 700), cv::FONT_HERSHEY_SIMPLEX, 2, cv::Scalar(0, 0, 0), 3);
            // for(int i=0;i<unmatched_static_id_show.size();i++)
            // {
            //     cv::putText(debug, std::to_string(unmatched_static_id_show[i]), cv::Point(out_width() - 400-200*i, 700), cv::FONT_HERSHEY_SIMPLEX, 2, cv::Scalar(0, 0, 0), 3);
            // }
#endif
        }

        // DetInputOutput get_dets()
        // {
        //     std::unique_lock<std::mutex> ulk(_muDet);
        //     return  _dets;
        // }
        // void set_dets(DetInputOutput &d)
        // {
        //     std::unique_lock<std::mutex> ulk(_muDet);
        //     _dets = d;
        // }
        // Status fetch_dets(DetInputOutput &dets)
        // {
        //     dets = get_dets();
        //     return CZCV_OK;
        // }

        //设置track的线程锁
        TrackerInputOutput get_tracks(bool primary=true)
        {
            if (primary)
            {
                std::unique_lock<std::mutex> ulk(_muTra);
                return  _tracks;
            }
            else
            {
                std::unique_lock<std::mutex> ulk(_muTraSub);
                return  _tracksSub;
            }
        }
        void set_tracks(TrackerInputOutput &t, bool primary=true)
        {
            if (primary)
            {
                std::unique_lock<std::mutex> ulk(_muTra);
                _tracks = t;
            }
            else
            {
                std::unique_lock<std::mutex> ulk(_muTraSub);
                _tracksSub = t;
            }        
        }
        Status fetch_tracks(TrackerInputOutput &tracks, bool primary=true)
        {
            tracks = get_tracks(primary);
            return CZCV_OK;
        }

        void set_tracks_with_gesture(TrackerInputOutput &t)
        {
            std::unique_lock<std::mutex> ulk(_muTraGes);
            _tracksWithGesture = t;
        }
        Status fetch_tracks_with_gesture(TrackerInputOutput &tracks)
        {
            std::unique_lock<std::mutex> ulk(_muTraGes);
            tracks = _tracksWithGesture;
            return CZCV_OK;
        } 

        std::vector<BboxF> get_tracksBox()
        {
            return  _tracksBox;
        }
        void set_tracksBox(std::vector<BboxF>& t)
        {
            _tracksBox.clear();
            _tracksBox = t;
        }
        
        std::vector<id_biClassification_infor> get_Idinfor()
        {
            return  _id_infor;
        }
        void set_Idinfor(std::vector<id_biClassification_infor>& t)
        {
            _id_infor = t;
        }

        // Status update_box_byPreTraResule(TrackerInputOutput& tracks_io, cv::Mat& img, czcv_camera::PersonAssertTNN* _biClassificationPtr)
        // {
        //     std::vector<BboxF> preTrackBox = get_tracksBox();
        //     std::vector<int> track_id_list;
        //     std::vector<int> track_id_pre_list;

        //     cv::Rect window(0, 0, img.cols, img.rows);
        //     for (auto box : tracks_io.in_bbox())
        //     {
        //         track_id_list.push_back(box.instance_id());
        //     }
        
        //     for (auto pre = preTrackBox.begin(); pre != preTrackBox.end(); pre++)
        //     {
        //         cv::Rect crop_roi = pre->cv_rect() & window;
        //         track_id_pre_list.push_back(pre->instance_id());
        //         if (find(track_id_list.begin(), track_id_list.end(), pre->instance_id()) == track_id_list.end())
        //         {
        //             bool f = _biClassificationPtr->run(rga_mat.phyaddr,crop_roi,img.cols,img.rows,1);
        //             if (f)
        //             {
        //                 pre->score(0.35);
        //                 pre->flag(true);
        //                 tracks_io.push_in_bbox(*pre);
        //                 tracks_io.push_tracked_bbox(*pre);
        //             }
        //         }
        //     }
            
        //     std::vector<int> delete_list;
        //     std::vector<BboxF> inbox = tracks_io.in_bbox();
        //     for (auto t = inbox.begin(); t != inbox.end(); t++)
        //     {
        //         if (find(track_id_pre_list.begin(), track_id_pre_list.end(), t->instance_id()) == track_id_pre_list.end())
        //         {
        //             for (auto Box : tracks_io.in_bbox())
        //             {
        //                 float score = (Box.cv_rect() & t->cv_rect()).area() * 1.0 / (Box.cv_rect().area() + t->cv_rect().area() - (Box.cv_rect() & t->cv_rect()).area());
        //                 if (score > 0.1 && Box.instance_id() != t->instance_id())
        //                 {
        //                     delete_list.push_back(Box.instance_id());
        //                 }
        //             }
        //         }
        //     }
        //     std::vector<BboxF> result_track = tracks_io.tracked_bbox();
        //     for (int i : delete_list)
        //     {
        //         for (auto t = result_track.begin(); t != result_track.end(); t++)
        //         {
        //             if (t->instance_id() == i)
        //             {                    
        //                 t = result_track.erase(t);
        //                 if (t == result_track.end()) break;
        //             }
        //         }
        //     }

        //     std::vector<BboxF> result_in = tracks_io.in_bbox();
        //     for (int i : delete_list)
        //     {
        //         for (auto t = result_in.begin(); t != result_in.end(); t++)
        //         {
        //             if (t->instance_id() == i)
        //             {                    
        //                 t = result_in.erase(t);
        //                 if (t == result_in.end()) break;
        //             }
        //         }
        //     }
        //     tracks_io.in_bbox(result_in);
        //     tracks_io.tracked_bbox(result_track);
        //     //std::vector<BboxF> temp = result;
        //     set_tracksBox(result_track);
       
        //     return CZCV_OK;
        // }

        Status update_tracks_byBiClassification(TrackerInputOutput &tracks, czcv_camera::PersonAssertModel* _biClassificationPtr, const czcv_camera::RgaMat & rga_mat)
        {
            std::vector<id_biClassification_infor>::iterator idinfor;
            std::vector<id_biClassification_infor> id_infor = get_Idinfor();        //获取当前的id信息
            std::vector<int> id_list;                                               //当前存在的id列表

            for (auto infor : id_infor)
            {
                id_list.push_back(infor.id);
            }
            cv::Mat bgr_biclass;
            tracks.ref_frame_to(bgr_biclass);
            cv::Rect window(0, 0, bgr_biclass.cols, bgr_biclass.rows);

            for (auto& box : tracks.in_bbox())
            {
                cv::Rect crop_roi = box.cv_rect() & window;
                for (idinfor = id_infor.begin(); idinfor != id_infor.end(); idinfor++)
                {
                    if (idinfor->id == box.instance_id())
                    {
                        if (idinfor->frame_count > _frame_interval_rejudge)
                        {
                            idinfor->flag = true;  //_biClassificationPtr->run(rga_mat.phyaddr, crop_roi,bgr_biclass.cols,bgr_biclass.rows);
                            box.flag(idinfor->flag);
                            idinfor->frame_count = 0;
                        }
                    }
                }
                if (find(id_list.begin(), id_list.end(), box.instance_id()) == id_list.end())
                {
                    bool f = true;//_biClassificationPtr->run(rga_mat.phyaddr, crop_roi,bgr_biclass.cols,bgr_biclass.rows);
                    id_infor.push_back(id_biClassification_infor{ box.instance_id(), 0, f });
                    box.flag(f);
                }

            }

            //id_infor自更新
            std::vector<id_biClassification_infor>::iterator it;
            for (it = id_infor.begin(); it != id_infor.end(); )
            {
                it->frame_count++;
                if (it->frame_count > _frame_interval_delete)
                {
                    it = id_infor.erase(it);
                    if (it == id_infor.end()) break;
                }
                else
                {
                    it++;
                }
            }

            //根据id筛选dets
            std::vector<BboxF> result_in;
            for (auto& b : tracks.in_bbox())
            {
                for (auto i : id_infor)
                {
                    if (b.instance_id() == i.id)
                    {
                        if(i.flag)
                        {
                            b.flag(i.flag);
                            result_in.push_back(b);
                        }
                    }
                } 
            }

            std::vector<BboxF> result_tra;
            for (auto& b : tracks.tracked_bbox())
            {
                for (auto i : id_infor)
                {
                    if (b.instance_id() == i.id)
                    {
                        if(i.flag)
                        {
                            b.flag(i.flag);
                            result_tra.push_back(b);
                        }
                    }
                } 
            }

            tracks.in_bbox(result_in);
            tracks.tracked_bbox(result_tra);
            
            return CZCV_OK;
        }

        // 判断手势背景是否接近白色
        bool is_background_white(const cv::Mat &bgr, const stGestureRecResult &gesture)
        {
            if (bgr.empty() || bgr.channels() != 3)
            {
                return false;
            }

            // 获取检测框坐标，使用rectf字段（x0, y0, x1, y1格式）
            int x0 = static_cast<int>(gesture.rectf.x0);
            int y0 = static_cast<int>(gesture.rectf.y0);
            int x1 = static_cast<int>(gesture.rectf.x1);
            int y1 = static_cast<int>(gesture.rectf.y1);
            int xc = (x0 + x1) / 2;
            int yc = (y0 + y1) / 2;
            int w = x1 - x0 + 1;
            int h = y1 - y0 + 1;
            w *= 1.5f;
            h *= 1.5f;

            // 边界检查
            x0 = std::max(0, std::min(xc - w / 2, bgr.cols - 1));
            y0 = std::max(0, std::min(yc - h / 2, bgr.rows - 1));
            x1 = std::max(0, std::min(xc + w / 2, bgr.cols - 1));
            y1 = std::max(0, std::min(yc + h / 2, bgr.rows - 1));

            if (x1 <= x0 || y1 <= y0)
            {
                return false;
            }

            // 提取检测框区域
            cv::Rect roi(x0, y0, x1 - x0, y1 - y0);
            cv::Mat gestureRegion = bgr(roi).clone();

            // 对图像进行中值滤波去噪
            cv::Mat blurred;
            cv::medianBlur(gestureRegion, blurred, 5);

            // 转换为灰度图
            cv::Mat gray;
            cv::cvtColor(blurred, gray, cv::COLOR_RGB2GRAY);

            // 计算白色区域的占比
            // 统计灰度值大于200的像素点数量
            const int whiteThreshold = 75;
            int whitePixels = 0;
            int totalPixels = gray.rows * gray.cols;

            for(int i = 0; i < gray.rows; i++) {
                for(int j = 0; j < gray.cols; j++) {
                    if(gray.at<uchar>(i,j) > whiteThreshold) {
                        whitePixels++;
                    }
                }
            }

            // 计算白色区域占比，大于50%认为是白色背景
            float whiteRatio = (float)whitePixels / totalPixels;
            //LOGE("is_background_white: %f", whiteRatio);
            bool isWhite = whiteRatio > 0.5;

            return isWhite;
        }

        bool filter_gesture_by_white_background(TrackerInputOutput &tracks, const cv::Mat &bgr)
        {
            std::vector<stGestureRecResult> &gestureResults = tracks.gestureResults();
            std::vector<stGestureRecResult> filteredResults;

            bool has_peace = false;
            for (const auto &gesture : gestureResults)
            {
                if (gesture.clsid != CZCV_GESTURE_PEACE && gesture.clsid != CZCV_GESTURE_PEACE_INV)
                {
                    filteredResults.push_back(gesture);
                    continue;
                }
                if (is_background_white(bgr, gesture))
                {
                    has_peace = true;
                    filteredResults.push_back(gesture);
                }
            }

            tracks.gestureResults(filteredResults);
            return has_peace;
        }

        void thread_loop_gesture(long long id)
        {
            if (_use_4k)
            {
                ges_set_is_running(false);
                return;
            }

            int down_scale = _down_scale;
#ifndef _WIN32
            int debug_mode = property_get_int32("vendor.czcv.camera.debug2", 0);
#else
            int debug_mode = 0;
#endif

            if (debug_mode)
            {
                down_scale = 1;
            }

            czcv_camera::RgaMat rga_mat;
            rga_mat.handle = _rgaops->malloc_rga(&rga_mat.viraddr,&rga_mat.phyaddr,_final_w / down_scale, _final_h / down_scale, czcv_pixel_format_t::HAL_PIXEL_FORMAT_RGB_888);
            if (rga_mat.handle == NULL)
            {   
                LOGE("thread_loop_gesture rga_mat malloc failed\n");
            }

            ges_set_is_running(true);

            std::shared_ptr<Yolov10RKNN> _gestureRecognitiongPtr;

            Status s;
            czcv_model_type_t modelType = EN_MODEL_TYPE_NPU;

            _gestureRecognitiongPtr.reset(new czcv_camera::Yolov10RKNN());
            _gestureRecognitiongPtr->set_primary(true);
            _gestureRecognitiongPtr->init(_handModelPaths, _rgaops, EN_MODEL_TYPE_GPU);            
        
            int frameCnt = -1;
            std::vector<stGestureRecResult> gestureResults;
            while(true)
            {
                AsyncRunnerEvent event = get_event();
                switch (event)
                {
                    case NORMAL_RUNNING:
                    {
                        TrackerInputOutput tracks;
                        fetch_tracks(tracks);
                        if (gesture_mode() == false)
                        {
                            set_tracks_with_gesture(tracks);
                            std::this_thread::sleep_for(std::chrono::milliseconds(10));
                            continue;
                        }   

                        if (frameCnt == tracks.frame_cnt())
                        {
                            tracks.gestureResults(gestureResults);
                            set_tracks_with_gesture(tracks);
                            std::this_thread::sleep_for(std::chrono::milliseconds(10));
                            continue;
                        }
                        frameCnt = tracks.frame_cnt();

                        if (tracks.in_bbox().size() == 0)
                        {
                            gestureResults.clear();
                            set_tracks_with_gesture(tracks);
                            std::this_thread::sleep_for(std::chrono::milliseconds(10));
                            continue;
                        } 
                
                        double start = cv::getTickCount();

                        cv::Mat bgr;
                        {
                            std::unique_lock<std::mutex> ulk(_muMatVec);                             
                            int ret = _rgaops->cropScaleRgb(_rga_mat.phyaddr,rga_mat.phyaddr,_final_w / down_scale,_final_h / down_scale,0,0,_final_w / down_scale,_final_h / down_scale,_final_w / down_scale,_final_h / down_scale,0,0,_final_w / down_scale,_final_h / down_scale);		                           
                            if (ret != 0)
                            {
                                LOGE("RGA cropScaleRgb failed: %d", ret);
                            }
                        }

                        bgr = cv::Mat(_final_h / down_scale, _final_w / down_scale, CV_8UC3, rga_mat.viraddr);
                        tracks.ref_frame_to(bgr);                       
                        tracks.input_phyadrr(rga_mat.phyaddr);
                      
                        _gestureRecognitiongPtr->run(tracks);

                        if (simulated_id > 0)
                        {
                            std::vector<stGestureRecResult>& gestureResults_temp = tracks.gestureResults();
                            gestureResults_temp.clear();
                            
                            if (in_simulated())
                            {
                                if (simulated_id % 400 == 0)
                                {
                                    std::vector<BboxF> windowsIn = tracks.in_bbox();
                                    std::srand(static_cast<unsigned int>(std::time(0))); // 或 std::srand(std::time(nullptr));
        
                                    // 生成 0 到 RAND_MAX 之间的随机整数
                                    int random_num = std::rand();
                                    // 生成一个 [0, 99] 范围内的随机整数
                                    gesture_x0 = (std::rand() % ((int)windowsIn[0].width() * _down_scale - 200) + windowsIn[0].xmin() + 100);
                                    gesture_x1 = gesture_x0 + std::rand() % 50 + 50;
                                    gesture_y0 = windowsIn[0].ymin() * _down_scale + 100 + std::rand() % 400;
                                    gesture_y1 = gesture_y0 + 50 + std::rand() % 100;
                                }
                                
                                stGestureRecResult gesture;
                                gesture.clsid = CZCV_GESTURE_PEACE;                                                           
                                gesture.rectf.x0 = gesture_x0 / _down_scale;
                                gesture.rectf.x1 = gesture_x1 / _down_scale;
                                gesture.rectf.y0 = gesture_y0 / _down_scale;
                                gesture.rectf.y1 = gesture_y1 / _down_scale;
                                gesture.det_instance_id = 0;
                                gestureResults_temp.push_back(gesture);
                            }
                        }     

                        bool has_peace = filter_gesture_by_white_background(tracks, bgr);

                        set_tracks_with_gesture(tracks);  
                        gestureResults = tracks.gestureResults();

                        if (has_peace)
                        {
                            windowUpdater.copy_to_rgamat(rga_mat.phyaddr);
                        }

                        double duration = ( (double)cv::getTickCount() - start) / cv::getTickFrequency();
                        int ms = duration * 1000;
                        LOGI("==> [thread_loop_gesture] fps:%d\n", ms);
                        std::this_thread::sleep_for(std::chrono::milliseconds(10));           
                    }
                    break;

                    case STOP_LOOP:
                    {
                        Status s;
                        s = _gestureRecognitiongPtr->release();
                        if (s != CZCV_OK)
                        {
                            LOGE("_gestureRecognitiongPtr release faild");
                        }
                        
                        if (_librgaopsLibHandle)
                        {                      
                            if (rga_mat.handle != NULL)
                            {
                                int ret = _rgaops->free(rga_mat.handle);
                                rga_mat.handle = nullptr;
                            }                                  
                        }                
                       
                        ges_set_is_running(false);
                        _gestureRecognitiongPtr.reset();

                        return;
                    }
                    default:
                        break;
                }
            }
        }

        void thread_loop(long long id, bool primary=true)
        {    
            set_is_running(true, primary);
            czcv_camera::RgaMat rga_mat;
		    czcv_camera::RgaMat rgamat_clone;

            if (primary)
            {
                opencl_init();
            }

            std::shared_ptr<BaseObjectDetector> _detectorPtr = czcv_camera::create_detector(_detectorId);
            if (nullptr == _detectorPtr)
            {
                LOGE("create detector failed!\n");
            }

            std::shared_ptr<BaseTracker> _trackerPtr = czcv_camera::create_tracker(_trackerId);        
            if(_trackerPtr == nullptr)
            {
                LOGE("create tracker failed!\n");
            }
            _trackerPtr->bind_detector(_detectorPtr);

            std::shared_ptr<PersonAssertModel> _biClassificationPtr; 

            std::shared_ptr<Yolov10RKNN> _gestureRecognitiongPtr;

            int down_scale = _down_scale;
            if(false == primary)
            {
                down_scale = _down_scale_sub;
            }
#ifndef _WIN32
            int debug_mode = property_get_int32("vendor.czcv.camera.debug2", 0);
#else
            int debug_mode = 0;
#endif
            if (debug_mode)
            {
                down_scale = 1;
            }

            if (!_use_4k)
            {
                Status s;
                czcv_model_type_t modelType = EN_MODEL_TYPE_NPU;
                
#define ONLY_NPU 1

// #ifndef ONLY_NPU
                

//                 if (primary)
//                 {
//                     modelType = EN_MODEL_TYPE_NPU;
//                 }
//                 else if (_only_cpu)
//                 {
//                     modelType = EN_MODEL_TYPE_CPU;
//                 }
//                 else
//                 {
//                     modelType = EN_MODEL_TYPE_GPU;
//                 }
// #else
//                 modelType = EN_MODEL_TYPE_NPU;
// #endif
                _detectorPtr->set_primary(primary);
                
                s = _detectorPtr->init(_modelPaths,_rgaops, modelType);
                if(s != CZCV_OK)
                {
                    LOGE("model init failed!\n");
                }

                _trackerPtr->set_primary(primary);

                rga_mat.handle = _rgaops->malloc_rga(&rga_mat.viraddr,&rga_mat.phyaddr,_final_w / down_scale, _final_h / down_scale, czcv_pixel_format_t::HAL_PIXEL_FORMAT_RGB_888);
                rgamat_clone.handle = _rgaops->malloc_rga(&rgamat_clone.viraddr,&rgamat_clone.phyaddr, _final_w / down_scale, _final_h / 2 / down_scale, czcv_pixel_format_t::HAL_PIXEL_FORMAT_RGB_888);
                if (rga_mat.handle == NULL || rgamat_clone.handle == NULL)
                {
                    LOGE("malloc_rga faild");
                }
                if (NULL == rga_mat.viraddr || NULL == rgamat_clone.viraddr)
                {
                    LOGE("viraddr is nullptr\n");
                }
                
                //_gestureRecognitiongPtr.reset(new czcv_camera::Yolov10RKNN());
                //_gestureRecognitiongPtr->set_primary(primary);
                //_gestureRecognitiongPtr->init(_handModelPaths, _rgaops, EN_MODEL_TYPE_GPU);               
            }
            
            int saveflag = 0;
            int dataindex = 0;
            int frameCnt = 0;
            bool save_flag = false;
            int lastdetectnum = 0;
            std::vector<BboxF> windowsInLast;
            std::vector<BboxF> windowsTraLast; 
            while(true)
            {
                AsyncRunnerEvent event = get_event();
                switch (event)
                {
                    case NORMAL_RUNNING:
                    {
                        if (!_use_4k)
                        {
                            double start = cv::getTickCount();
                            cv::Mat frame(_final_h*1.5 / down_scale,_final_w / down_scale,CV_8UC1,rgamat_clone.viraddr);
                            //如果帧还没有准备好，那么轮询等待
                            if(!frame_ready(primary))
                            {
                                //LOGE("not ready...\n");
                                std::this_thread::sleep_for(std::chrono::milliseconds(5));
                                continue;
                            }

                            //double t1 = cv::getTickCount();
                            int low_consumption = get_consumption();
                            if(low_consumption == 1)
                            {
                                std::this_thread::sleep_for(std::chrono::seconds(1));                              
                            }

                            if (primary)
                            {
                                get_frame(rgamat_clone.phyaddr, phy_addr(), _rgaops, true, down_scale);
                            }
                            else
                            {
                                get_frame(rgamat_clone.phyaddr, phy_addr_sub(), _rgaops, true, down_scale);
                            }
                            //cv::Mat frame_vir;
                            //get_frame(frame_vir, true);
                            
                            if(!frame.data)
                            {
                                LOGE("no valid data...\n");
                                continue;
                            }

                            set_frame_ready(false, primary);

                            if (mode() == enTrackModeNo && gesture_mode() == false)
                            {
                                TrackerInputOutput tracks;
                                //set_tracks(tracks);

                                set_tracks(tracks, primary);
                                std::this_thread::sleep_for(std::chrono::milliseconds(100));
                                continue;
                            }               

                            DetInputOutput dets;
                            
                            cv::Mat bgr;
                            //cv::Mat bgr_vir;
                            if (_bnv12 == true)
                            {
                                dets.format(NV12_format);
                                int ret = _rgaops->yuv2rgb(rgamat_clone.phyaddr,rga_mat.phyaddr,_final_w / down_scale,_final_h / down_scale,_final_w / down_scale,_final_h / down_scale);
                                if (ret != 0)
                                {
                                    LOGE("yuv2rgb faild");
                                }
                                bgr = cv::Mat(_final_h / down_scale, _final_w / down_scale, CV_8UC3, rga_mat.viraddr);
                                //cvtColor(frame_vir, bgr_vir, cv::COLOR_YUV2BGR_NV12);
                            }
                            else
                            {
                                dets.format(NV21_format);
                                cv::cvtColor(frame, bgr, cv::COLOR_YUV2BGR_NV21);
                            }
                            //设置检测时间
                            if (primary)
                            {
                                gettimeofday(&detect_time, NULL);
                            }
                            else
                            {
                                gettimeofday(&detect_time_sub, NULL);
                            }
                            
                            dets.ref_frame_from(bgr);
                            dets.set_input_phyadrr(rga_mat.phyaddr);

                            czcv_camera::DynamicParams params;
                               
                            _detectorPtr->run(dets, params); 

                            if (simulated_id > 0)
                            {
                                dets.clean_bbox();
                                if (in_simulated())
                                {
                                    int x = 0;
                                    if (simulated_id % 400 == 0)
                                    {
                                        std::srand(static_cast<unsigned int>(std::time(0))); // 或 std::srand(std::time(nullptr));
    
                                        // 生成 0 到 RAND_MAX 之间的随机整数
                                        int random_num = std::rand();
                                        
                                        // 生成一个 [0, 99] 范围内的随机整数
                                        simulated_x0 = std::rand() % 3000;
                                        int xoffset = std::rand() % (std::min(400, 3400 - simulated_x0)) + 400;
                                        simulated_x1 = xoffset + simulated_x0;
                                        simulated_y0 = std::rand() % 300;
                                        simulated_y1 = 1800 + std::rand() % 300;
                                    }

                                    BboxF box;
                                    box.xmin(simulated_x0 / _down_scale);
                                    box.xmax(simulated_x1 / _down_scale);
                                    box.ymin(simulated_y0 / _down_scale);
                                    box.ymax(simulated_y1 / _down_scale);
                                    box.class_id(0);
                                    box.instance_id(0);
                                    box.score(1.0f);

                                    LOGE("det box: %f %f %f %f", simulated_x0, simulated_x1, simulated_y0, simulated_y1);

                                    dets.push_one_bbox(box);
                                }         
                            }
                                              
                            std::vector<BboxF> det_boxes = dets.bbox();                       
                            
                            dets.clean_bbox();
                            std::vector<BboxF> face_boxes;
                            for (auto & box: det_boxes)
                            {
                                if (box.class_id() == 0)
                                {
                                    dets.push_one_bbox(box);
                                }
                                else
                                {
                                    face_boxes.push_back(box);
                                }
                            }
                                          
                            // LOGE("detect output: %d, primary: %d\n", dets.bbox().size(), primary);
                            // if (dets.bbox().size() < lastdetectnum && frameCnt > 15 && (save_flag == false) && primary)
                            // {
                            //     LOGE("save_flag");
                            //     cv::imwrite("/mnt/img/test.jpg", bgr);
                            //     save_flag = true;
                            // }
                            //lastdetectnum = dets.bbox().size();

                            //double t4 = cv::getTickCount();

    #if DEBUG_MODE
                            char recFileNametrack[128] = "/mnt/imgs/cam_det_before";
                            sprintf(recFileNametrack, "%s%d.bat",recFileNametrack,  g_index);
                            FILE *  recFiledet = fopen(recFileNametrack, "wb");
                            if(NULL != recFiledet) {
                                for (auto & box: dets.bbox())
                                {
                                    float abox[4] = {box.xmin(), box.ymin(), box.xmax(), box.ymax()};
                                    fwrite(abox, 4 * sizeof(float), 1, recFiledet);                               
                                }  
                                fclose(recFiledet);                         
                            }  
                            else{
                                int errNum = errno;
                                //LOGE("open fail errno = %d reason = %s \n", errNum, strerror(errNum));
                            }                   
    #endif
                            //开启跟踪
                            TrackerInputOutput tracks;
                            tracks.ref_frame_from(bgr);
                            tracks.in_bbox(dets.bbox());
                            tracks.input_phyadrr(rga_mat.phyaddr);
                            _trackerPtr->run(tracks, params,_use_4k);

                            //double t5 = cv::getTickCount();

                            update_tracks_byBiClassification(tracks, _biClassificationPtr.get(), rga_mat);  

                            if (false)
                            {
                                std::vector<BboxF> windowsIn = tracks.in_bbox();
                                std::vector<BboxF> windowsTra = tracks.tracked_bbox(); 
                                
                                bool lose_one = false;
                                for (auto & tracklast: windowsTraLast)
                                {             
                                    bool bfind = false; 
                                    for (auto & track: windowsTra)
                                    {
                                        if (track.instance_id() == tracklast.instance_id())
                                        {
                                            bfind = true;
                                            break;
                                        }
                                    }

                                    lose_one |= (bfind == false);
                                }

                                lose_one = false;
                                if (lose_one)
                                {
                                    for (auto & tracklast: windowsTraLast)
                                    {
                                        LOGE("last track id: %d", tracklast.instance_id());
                                    }
                                    for (auto & track: windowsTra)
                                    {
                                        LOGE("current track id: %d", track.instance_id());
                                    }
                                    std::string fpath = "/mnt/img/" + std::to_string(dataindex) + ".bin";
                                    FILE* f = fopen(fpath.c_str(), "wb");
                                    if (f != NULL)
                                    {
                                        fwrite(rga_mat.viraddr, 1, _final_h / down_scale * _final_w / down_scale * 3,f);
                                        fclose(f);
                                    }

                                    std::string fpath_last = "/mnt/img/" + std::to_string(dataindex) + "_last.bin";
                                    FILE* flast = fopen(fpath_last.c_str(), "wb");
                                    if (flast != NULL)
                                    {
                                        fwrite(_rga_mat.viraddr, 1, _final_h / down_scale * _final_w / down_scale * 3,flast);
                                        fclose(flast);
                                    }

                                    dataindex++;
                                    if (dataindex > 10)
                                    {
                                        dataindex = 0;
                                    }
                                }

                                windowsInLast = windowsIn;
                                windowsTraLast = windowsTra;
                            }

                            for (auto & box : face_boxes)
                            {
                                tracks.push_in_bbox(box);
                            }

                            //_gestureRecognitiongPtr->run(tracks);  
                            if (primary && gesture_mode()) 
                            {
                                {
                                    std::unique_lock<std::mutex> ulk(_muMatVec); 
                                    //tracks.copy_frame_from(bgr);
                                    int ret = _rgaops->cropScaleRgb(rga_mat.phyaddr,_rga_mat.phyaddr,bgr.cols,bgr.rows,0,0,bgr.cols,bgr.rows,bgr.cols,bgr.rows,0,0,bgr.cols,bgr.rows);		
                                    if (ret != 0)
                                    {
                                        LOGE("rga cropScaleRgb failed: %d", ret);
                                    }
                                }
                                tracks.input_phyadrr(_rga_mat.phyaddr);
                            }

                            tracks.frame_cnt(frameCnt);
                            frameCnt++;
                            set_tracks(tracks, primary);      

                            // double t6 = cv::getTickCount();

                            // LOGI("[czcv_camera]primary: %d, data ready time:%f, preprocess time: %f, detect time: %f, track time:%f, classify time: %f\n", 
                            //         primary, (t2 - t1) * 1000 / cv::getTickFrequency(),
                            //         (t3 - t2) * 1000 / cv::getTickFrequency(), 
                            //         (t4 - t3) * 1000 / cv::getTickFrequency(),
                            //         (t5 - t4) * 1000 / cv::getTickFrequency(),
                            //         (t6 - t5) * 1000 / cv::getTickFrequency());
                            
    #if DEBUG_MODE
                            char recFileNamedet[128] = "/mnt/imgs/cam_det_after";
                            sprintf(recFileNamedet, "%s%d.bat",recFileNamedet,  g_index);
                            recFiledet = fopen(recFileNamedet, "wb");
                            if(NULL != recFiledet) {
                                for (auto & box: dets.bbox())
                                {
                                    float abox[4] = {box.xmin(), box.ymin(), box.xmax(), box.ymax()};
                                    fwrite(abox, 4 * sizeof(float), 1, recFiledet);                               
                                }  
                                fclose(recFiledet);                         
                            }  
                            else{
                                int errNum = errno;
                                //LOGE("open fail errno = %d reason = %s \n", errNum, strerror(errNum));
                            }
    #endif
    #if DEBUG_MODE
                            char recFileName[128] = "/mnt/imgs/cam_out_dump";
                            sprintf(recFileName, "%s%d.rgb",recFileName,  g_index);
                            FILE * recFile = fopen(recFileName, "wb");
                            void * mapAddr = bgr.data;
                            unsigned int size = bgr.cols * bgr.rows * 3;
                            if(NULL != recFile) {
                                //LOGE("buf.m.planes->data_offset: 0x%08x, buf.m.planes->length: %d", 
                                //                               mapAddr, size);
                                fwrite((void *)mapAddr, size, 1, recFile);
                                fclose(recFile);
                            }
                            else{
                                int errNum = errno;
                                //LOGE("open fail errno = %d reason = %s \n", errNum, strerror(errNum));
                            }

                            recFile = fopen("/mnt/imgs/index.txt", "w");
                            if(NULL != recFile) {
                                fprintf(recFile, "%d\n", g_index);
                                fclose(recFile);
                            }
                            
                            g_index++;
                            if (g_index > 500)
                            {
                                g_index = 0;
                            }
    #endif
                            double duration = ( (double)cv::getTickCount() - start) / cv::getTickFrequency();
                            int ms = duration * 1000;
                            //int maxMS = 1000 / _maxFPS;

                            LOGI("==> [Async_Preview_Runner] fps:%d, primary: %d\n", ms, primary);
                            std::this_thread::sleep_for(std::chrono::milliseconds(60));
                        }
                        else
                        {
                            if (mode() == enTrackModeNo)
                            {
                                TrackerInputOutput tracks;
                                set_tracks(tracks);
                                std::this_thread::sleep_for(std::chrono::milliseconds(100));
                                continue;
                            }
                            
                            DetInputOutput dets;
                            double start = cv::getTickCount();
                            //设置检测时间
                            if (primary)
                            {
                                gettimeofday(&detect_time, NULL); 
                            }
                            else
                            {
                                gettimeofday(&detect_time_sub, NULL); 
                            }
                            
                            std::vector<PersonBBox> personBoxes = person_boxes();
                            std::vector<BboxF> face_boxs;
                            std::vector<BboxF> person_boxs;
                            int i = 0;
                            for (auto p_box : personBoxes)
                            {
                                face_boxs.push_back(BboxF((float)p_box.person_x0,(float)p_box.person_y0,(float)p_box.person_x1,(float)p_box.person_y1,p_box.person_conf,1));
                                i += 1;
                            }
                            for (auto face_box : face_boxs)
                            {
                                float xmin = MAX(face_box.xmin() - 1.5*face_box.width(),0);
                                float ymin = face_box.ymin();
                                float xmax = MIN(face_box.xmax() + 1.5*face_box.width(),3839);
                                float ymax = MIN((face_box.ymax() + 2*face_box.height()),2159);
                                BboxF bboxF(xmin, ymin, xmax, ymax, 1,0);
                                person_boxs.push_back(bboxF);
                            }                            

                            czcv_camera::DynamicParams params;
                            TrackerInputOutput tracks;
                            tracks.in_bbox(person_boxs);
                            _trackerPtr->run(tracks, params,_use_4k);

                            for (auto & box : face_boxs)
                            {
                                tracks.push_in_bbox(box);
                            }
                            set_tracks(tracks);
                            std::this_thread::sleep_for(std::chrono::milliseconds(100));
                        }
                    }
                    break;

                    case STOP_LOOP:
                    {
                        if (!_use_4k)
                        {
                            Status s;
                            s = _detectorPtr->release();
                            if (s != CZCV_OK)
                            {
                                LOGE("_detectorPtr release faild");
                            }
                            // s = _biClassificationPtr->release();
                            // if (s != CZCV_OK)
                            // {
                            //     LOGE("_biClassificationPtr release faild");
                            // }
                            // s =_gestureRecognitiongPtr->release();
                            // if (s != CZCV_OK)
                            // {
                            //     LOGE("_gestureRecognitiongPtr release faild");
                            // }
                        }
                        if (_librgaopsLibHandle)
                        {                         
                            int ret = 0;
                            if (rga_mat.handle != NULL)
                            {
                                ret = _rgaops->free(rga_mat.handle);
                                rga_mat.handle = nullptr;
                            }
                            if (ret != 0)
                            {
                                LOGE("rga_mat free error");
                            }   
                            if (rgamat_clone.handle != NULL)
                            {
                                ret = _rgaops->free(rgamat_clone.handle);
                                rgamat_clone.handle = nullptr;
                            }
                            if (ret != 0)
                            {
                                LOGE("rgamat_clone free error");
                            }                                                                                                                                          
                        }                

                        _detectorPtr.reset();
                        _trackerPtr.reset();
                        _biClassificationPtr.reset();
                        _gestureRecognitiongPtr.reset();
                        set_is_running(false, primary);

                        return;
                    }
                    default:
                        break;
                }
            }
        }

        void set_nv12(bool bnv12)
        {
            _bnv12 = bnv12;
        }

        bool doa_is_running()
        {
            std::unique_lock<std::mutex> ulk(_muDoaIsRun);
            return _isDoaRunning;
        }
        void set_doa_is_running(bool v)
        {
            std::unique_lock<std::mutex> ulk(_muDoaIsRun);
            _isDoaRunning = v;
        }

        float majorityElement(std::vector<float>& nums) {
            std::map<float, float> ref;
            int len = nums.size();
            for (int i = 0; i < len; i++) {
                if (ref.find(nums[i]) == ref.end())
                    ref[nums[i]] = 1; //添加key
                else
                    ref[nums[i]]++;
            }
            for (std::map<float, float>::iterator it = ref.begin(); it != ref.end(); it++) {
                if (it->second > len / 2)
                    return it->first;
            }
            return -1;
        }
float _tmpdoa = 0;
        void get_doa()
        {
            if (_doa_callback == nullptr)
            {
                //LOGE("_doa_callback is null");
                return;
            }

#ifdef __ANDROID__
            //_doa = static_cast<float>(_client->getDoa());
            DoAInfo stDoaInfo;  
                     
            int ret = _doa_callback(&stDoaInfo);
            if (ret != 0)
            {
                return;
            }
            _doa = stDoaInfo.angle;
            
            timeval temp;
            gettimeofday(&temp, NULL);
            _tmpdoa = _doa;
            //LOGE("=============================================================当前doa值:      %f ",_doa);
            if (!(_doa > 210 && _doa < 330))
            {
                _doa=-1;
            }
            
            std::unique_lock<std::mutex> ulk(_muDoa);         
            _doaRecordArray[_doaRecordIndex] = {_doa,temp};
            // _doaRecordArray[_doaRecordIndex].doa=_doa;
            // _doaRecordArray[_doaRecordIndex].time_stamp=temp;
            _doaRecordIndex++;
            if (_doaRecordIndex >= _doaRecordNum)
            {
                _doaRecordIndex = 0;               
            }

#endif
        }

        float read_doa(float* realdoa)
        {
            std::unique_lock<std::mutex> ulk(_muDoa);
#ifdef __ANDROID__
            *realdoa=_doa;
            
#endif
            // detection_doa=int(_doa);
            return _doa;
        }

        float read_doa(timeval &t)
        {
            float doa=-1;
            std::unique_lock<std::mutex> ulk(_muDoa);
         
#ifdef __ANDROID__
            double t_min=doa_wait_milliseconds*_doaRecordNum;
            int index=-1;
            for(int i=0;i< _doaRecordNum;i++)
            {
                double time_dis =1000.0 * (t.tv_sec - _doaRecordArray[i].time_stamp.tv_sec) + (t.tv_usec - _doaRecordArray[i].time_stamp.tv_usec) / 1000.0;
                
                if(time_dis>0 && abs(time_dis)<t_min)
                {
                    t_min=time_dis;
                    doa=_doaRecordArray[i].doa;
                    index=i;
                }
            }
            if(t_min>1000)
            {
                doa=-1;
                index=-1;
            }

            std::vector<float> doas_mini;
            if (index != -1 && _doaRecordArray.size() >= 30)
            {
                if (index >= 6)
                {
                    for (int i = 0; i < 7; i++)
                    {
                        doas_mini.push_back(_doaRecordArray[index-i].doa);
                    }
                }
                if (index < 6)
                {
                    for (int i = 0; i <= index; i++)
                    {
                        doas_mini.push_back(_doaRecordArray[i].doa);
                    }
                    int be_left = 7 - doas_mini.size();
                    for (int i = 0; i < be_left; i++)
                    {
                        doas_mini.push_back(_doaRecordArray[29-i].doa);
                    }   
                }
                doa = majorityElement(doas_mini);
            }

            

            // detection_doa=int(doa);
#endif

#if DEBUG_SHOW_MODE
            detection_doa=int(doa);
#endif
            return doa;
        }

        void thread_doa_loop(long long id)
        {
#ifdef __ANDROID__
            // _client = std::make_unique<czalg::AudioVideoServiceClientInterface>();
            // if (nullptr == _client)
            // {
            //     LOGE("get AudioVideoServiceClientInterface NULL\n");
            //     return;
            // }

            set_doa_is_running(true);

            while(true)
            {
                AsyncRunnerEvent event = get_event();
                switch (event)
                {
                    case NORMAL_RUNNING:                 
                        get_doa();

                        if (id < threadid())
                        {
                            LOGD("AsyncFrameWorker::thread_doa_loop async stopped!\n");
                            set_doa_is_running(false);
                            return;
                        }

                        std::this_thread::sleep_for(std::chrono::milliseconds(doa_wait_milliseconds));
                    break;

                    case STOP_LOOP:
                    {
                        LOGD("AsyncFrameWorker::thread_doa_loop stopped!\n");
                        //_event = NORMAL_RUNNING;
                        set_doa_is_running(false);
                        return;
                    }
                    default:
                        break; 
                }                                             
            }
#endif
        }

        void dst_vir_addr(void* dst_vir_addr)
        {
            _dstVirAddr = dst_vir_addr;
        }

        void dst_phy_addr(int dst_phy_addr)
        {
            _dstPhyAddr = dst_phy_addr;
        }

        void* dst_vir_addr()
        {
            return _dstVirAddr;
        }

        int dst_phy_addr()
        {
            return _dstPhyAddr;
        }

        void mode(enTrackMode mode)
        {
            std::unique_lock<std::mutex> ulk(_muTrackMode);
            _enTrackMode = mode;
        }

        enTrackMode mode()
        {
            std::unique_lock<std::mutex> ulk(_muTrackMode);
            return _enTrackMode;
        }

        void gesture_mode(bool mode)
        {
            std::unique_lock<std::mutex> ulk(_muGesMode);
            _bGestureMode = mode;
        }

        bool gesture_mode()
        {
            std::unique_lock<std::mutex> ulk(_muGesMode);
            return _bGestureMode;
        }

        void out_width(int out_width)
        {
            _outWidth = out_width;
        }

        int out_width()
        {
            return _outWidth;
        }

        void out_height(int out_height)
        {
            _outHeight = out_height;
        }
        
        int out_height()
        {
            return _outHeight;
        }

        long long threadid()
        {
            std::unique_lock<std::mutex> ulk(_muId);
            return m_threadid;
        }

        void threadid(long long id)
        {
            std::unique_lock<std::mutex> ulk(_muId);
            m_threadid = id;
        }

        void set_consumption(int low_consumption)
        {
            _low_consumption = low_consumption;
        }

        int get_consumption()
        {
            return _low_consumption;
        }

        void person_boxes(const std::vector<PersonBBox> & person_boxes,void* vir_addr, int width, int height)
        {
            _personBoxes = person_boxes;
        }

        std::vector<PersonBBox> person_boxes()
        {
            return _personBoxes;
        }
        
        void view_window(BboxF& window)
        {
            frame_worker_4k();
            window = _call_back_box;
        }

        void doa_callback(czcv_doa_callback callback)
        {
            _doa_callback = callback;
        }

        void* get_rga_handle()
        {
            return _librgaopsLibHandle;
        }

        void gesture_event_callback(czcv_gesture_event_callback callback)
        {
            _gesture_event_callback = callback;
        }

        void camera_led_callback(czcv_camera_led_callback callback)
        {
            _camera_led_callback = callback;
        }

        void assert_path(std::string assertpath)
        {
            _assertpath = assertpath;
        }

        std::string assert_path()
        {
            return _assertpath;
        }

      
    private:
        int _low_consumption = 0;
        std::mutex _muDet, _muDoa, _muDoaIsRun, _muTra, _muId, _muPhyAddr, _muTrackMode, _muPhyAddrSub, _muTraSub, _muTraGes, _muGesMode;
        std::thread _t;
        std::thread _t1;
        std::thread _t2;
        std::shared_ptr<Base_PersonViewer> _personViewerPtr = nullptr;

        DetectorID _detectorId;
        TrackerID _trackerId;

        int _use_4k;

        std::vector<id_biClassification_infor> _id_infor;       //存储id相关信息，frame_count：自id出现后开始计算目前间隔帧数
        int _frame_interval_rejudge = 10;                        //重新进行二分类的间隔
        int _frame_interval_delete = 20;                        //消失20帧后，删除该id信息（设置数值应大于跟踪设定的值）

        //Async_Preview_Runner _runner;
        WindowUpdater windowUpdater;
        WindowUpdater windowUpdaterSub;
        DynamicParams _gparams;
        cv::Mat _outframe;
        bool _outframeReady;
        std::mutex _muoutFrame;
        //bool _oneWin = true;
        unsigned char* _dataptr = nullptr;
        bool _bnv12 = false;
        void* _librgaopsLibHandle = nullptr;
        int _phyaddr = -1;
        int _phyaddr_sub = -1;
        czcv_camera::RgaMat sub_buf_mat;
        int _previeww = -1;
        int _previewh = -1; 
        int _final_w = -1;
        int _final_h = -1;
        DetInputOutput _dets;
        TrackerInputOutput _tracks;
        TrackerInputOutput _tracksSub;
        TrackerInputOutput _tracksWithGesture;
        std::vector<BboxF> _tracksBox;
        std::vector<std::string> _modelPaths;
        std::vector<std::string> _assertmodelPaths;
        std::vector<std::string> _handModelPaths;
        int _only_cpu;
        void* _tempVirAddr = nullptr;

        void* _dstVirAddr = nullptr;
        int _dstPhyAddr = -1;
        std::thread _tDoa;
#ifdef __ANDROID__
        //std::unique_ptr<czalg::AudioVideoServiceClientInterface> _client = nullptr;
#endif
		
        float _doa = -1;
        constexpr static int _doaRecordNum = 30;
        std::vector<doa_infor> _doaRecordArray;
        int _doaRecordIndex = 0;
        int doa_wait_milliseconds=50;

        bool _isDoaRunning = false;
        enTrackMode _enTrackMode = enTrackModeVideoandAudio;
        
        int _outWidth = 0;
        int _outHeight = 0;

        long long m_threadid = 0;

        czcv_camera::BboxF _call_back_box;
        std::vector<PersonBBox> _personBoxes;
        czcv_doa_callback _doa_callback = nullptr;
        czcv_gesture_event_callback _gesture_event_callback = nullptr;
        czcv_camera_led_callback _camera_led_callback = nullptr;
        timeval _last_led_time = {0,0};
        int _led_blink_times = 0;
        int _led_blink_count = 0;  
        int _led_blink_interval = 0; //ms
        timeval detect_time={0,0};
        timeval detect_time_sub={0,0};

        bool _bGestureMode = false;

        std::vector<stGestureInfo> _gesture_info;
        stGestureInfo _last_gesture_info;

        BboxF _last_win;
        BboxF _last_move_zoom_win;
        enZoomMoveState _zoom_move_state = EN_ZOOM_MOVE_STATE_STOP;

        std::vector<stGestureInfo> _gesture_info_sub;
        stGestureInfo _last_gesture_info_sub;

        BboxF _last_win_sub;
        BboxF _last_move_zoom_win_sub;
        enZoomMoveState _zoom_move_state_sub = EN_ZOOM_MOVE_STATE_STOP;

        std::mutex _muMatVec;
        czcv_camera::RgaMat _rga_mat;

        std::string _assertpath;
        int _down_scale = 3;   
        int _down_scale_sub = 12;
        std::shared_ptr<rga_interface_t> _rgaops;
    };

    Status PersonCenterStager::start()
    {
        return _impl->start();
    }
    Status PersonCenterStager::push_frame(ImageBlob &imgBlob)
    {
        return _impl->push_image_blob(imgBlob);
    }

    Status PersonCenterStager::bind_viewer(std::shared_ptr<Base_PersonViewer> &personViewerPtr)
    {
        return _impl->bind_viewer(personViewerPtr);
    }
    Status PersonCenterStager::init_models_async(std::vector<std::string>& detModelCfg, int only_cpu)
    {
        return _impl->init_models_async(detModelCfg, only_cpu);
    }
    Status PersonCenterStager::init_model_assert(std::vector<std::string>& assertModelCfg, int only_cpu)
    {
        return _impl->init_model_assert(assertModelCfg, only_cpu);
    }

    Status PersonCenterStager::init_model_hand(std::vector<std::string>& assertModelCfg, int only_cpu)
    {
        return _impl->init_model_hand(assertModelCfg, only_cpu);
    }
    Status PersonCenterStager::set_detector(DetectorID detectorId)
    {
        return _impl->set_detector(detectorId);
    }
   Status PersonCenterStager::set_tracker(TrackerID trackerId)
   {
       return _impl->set_tracker(trackerId);
   }
   Status PersonCenterStager::on_set_arg(DynamicParams& params)
   {
       return _impl->on_set_arg(params);
   }
    PersonCenterStager::PersonCenterStager(int preview_width, int preview_height, LOG_LEVEL logLevel)
    {
        _impl = std::make_shared<PersonCenterStagerImpl>(preview_width, preview_height, logLevel);
    }
    PersonCenterStager::~PersonCenterStager() {}

    Status PersonCenterStager::run(ImageBlob &imgBlob ,int Cmode,int low_consumption)
    {
        return _impl->run(imgBlob,Cmode,low_consumption);
    }

    Status PersonCenterStager::run_sub(ImageBlob &imgBlob)
    {
        return _impl->run_sub(imgBlob);
    }

    void PersonCenterStager::set_use4k(int use_4k)
    {
        _impl->set_use4k(use_4k);
    }

    void PersonCenterStager::set_nv12(bool bnv12)
    {
        _impl->set_nv12(bnv12);
    }

    void PersonCenterStager::release()
    {
        _impl->release();
    }

    void PersonCenterStager::dst_vir_addr(void* dst_vir_addr)
    {
        _impl->dst_vir_addr(dst_vir_addr);
    }

    void PersonCenterStager::dst_phy_addr(int dst_phy_addr)
    {
        _impl->dst_phy_addr(dst_phy_addr);
    }
    void PersonCenterStager::mode(enTrackMode mode)
    {
        _impl->mode(mode);
    }

    void PersonCenterStager::out_width(int out_width)
    {
        _impl->out_width(out_width);
    }
    void PersonCenterStager::out_height(int out_height)
    {
        _impl->out_height(out_height);
    }

    void PersonCenterStager::stop_async()
    {
        _impl->stop_async();
    }

    void PersonCenterStager::person_boxes(const std::vector<PersonBBox> & person_boxes,void* vir_addr, int width, int height)
    {
        _impl->person_boxes(person_boxes,vir_addr,width,height);
    }

    void PersonCenterStager::view_window(BboxF& window)
    {
        _impl->view_window(window);
    }

    void PersonCenterStager::doa_callback(czcv_doa_callback callback)
    {
        _impl->doa_callback(callback);
    }

    void* PersonCenterStager::get_rga_handle()
    {
        return _impl->get_rga_handle();
    }

    void PersonCenterStager::gesture_event_callback(czcv_gesture_event_callback callback)
    {
        _impl->gesture_event_callback(callback);
    }

    void PersonCenterStager::gesture_mode(bool mode)
    {
        _impl->gesture_mode(mode);
    }

    void PersonCenterStager::camera_led_callback(czcv_camera_led_callback callback)
    {
        _impl->camera_led_callback(callback);
    }

    void PersonCenterStager::assert_path(std::string assertpath)
    {
       _impl->assert_path(assertpath);;
    }
}

